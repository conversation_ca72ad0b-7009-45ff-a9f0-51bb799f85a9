<?php
namespace App\Providers;

use App\Services\CodeService;
use App\Services\ImageService;
use App\Services\LimiterService;
use App\Services\SmsService;
use App\Services\UploadService;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{

    public $singletons = [
        'upload'  => UploadService::class,
        'image'   => ImageService::class,
        'code'    => CodeService::class,
        'limiter' => LimiterService::class,
        'sms'     => SmsService::class,
    ];

    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        JsonResource::withoutWrapping();
    }

}
