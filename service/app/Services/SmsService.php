<?php
namespace App\Services;

use AlibabaCloud\Credentials\Credential;
use AlibabaCloud\SDK\Dysmsapi\V20180501\Dysmsapi;
use AlibabaCloud\SDK\Dysmsapi\V20180501\Models\SendMessageToGlobeRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use Darabonba\OpenApi\Models\Config;
use \Exception;

class SmsService
{
    /**
     * Initialize the Client with the credentials
     * @return Dysmsapi Client
     */
    public static function createClient()
    {
        // It is recommended to use the default credential. For more credentials, please refer to: https://www.alibabacloud.com/help/en/alibaba-cloud-sdk-262060/latest/credentials-settings-1.
        // $credential = new Credential();
        $config = new Config([
            "accessKeyId"     => config('system.aliyun_access_key'),
            "accessKeySecret" => config('system.aliyun_access_secret'),
        ]);
        // See https://api.alibabacloud.com/product/Dysmsapi.
        $config->endpoint = "dysmsapi.ap-southeast-1.aliyuncs.com";
        return new Dysmsapi($config);
    }

    /**
     * @param string[] $args
     * @return void
     */
    public static function send($phone, $params)
    {
        $client                    = self::createClient();
        $sendMessageToGlobeRequest = new SendMessageToGlobeRequest([
            "from"    => config('system.aliyun_sign'),
            "to"      => $phone,
            "message" => json_encode($params),

        ]);
        $runtime = new RuntimeOptions([]);
        try {
            // Copy the code to run, please print the return value of the API by yourself.
            $client->sendMessageToGlobeWithOptions($sendMessageToGlobeRequest, $runtime);
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // Only a printing example. Please be careful about exception handling and do not ignore exceptions directly in engineering projects.
            // print error message
            var_dump($error->message);
            // Please click on the link below for diagnosis.
            var_dump($error->data["Recommend"]);
            Utils::assertAsString($error->message);
        }

    }
}
