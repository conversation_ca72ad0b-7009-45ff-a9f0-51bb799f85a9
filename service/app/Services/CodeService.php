<?php
namespace App\Services;

use App\Models\User;
use App\Notifications\EmailCodeNotification;
use Illuminate\Support\Facades\Cache;

class CodeService
{
    private $user;
    private $code;
    public function send(User $user, string $driver)
    {
        app('limiter')->run('send_code', 1, 20);
        $this->user = $user;
        $this->code = $this->getCode();
        switch ($driver) {
            case 'mobile':
                $this->mobile();
                break;
            case 'email':
            default:
                $this->email();
        }
    }

    private function email()
    {
        $this->user->notify(new EmailCodeNotification($this->code));
    }

    private function mobile()
    {
        // TODO: send code service is not active
        // app('sms')->send($this->user->mobile, ['code' => $this->code], );

        // TEST: test only
        dd($this->user->mobile, $this->code);
    }

    private function getCode()
    {
        // FIXME: send_code now is sharing the key with all users
        Cache::add('send_code', mt_rand(1000, 9999), 600);
        return Cache::get('send_code');
    }
}
