<?php
namespace App\Services;

use Illuminate\Http\UploadedFile;

class UploadService
{
    public function run(UploadedFile $file, string $action)
    {
        return $this->$action($file);
    }

    private function local(UploadedFile $file)
    {
        // save into public/attachments/yearmonth/random_filename.ext
        $url = $file->storeAs('attachments/' . date('Ym'), $file->hashName(), 'public');

        return ['url' => url($url)];
    }
}
