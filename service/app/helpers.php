<?php
// check if user is admin

use Illuminate\Support\Facades\Auth;

function isAdministrator()
{
    return Auth::check() && Auth::user()->isAdmin;
}

function user(string $field)
{
    return Auth::check() && Auth::user()->$field;
}

function getModel()
{
    // get model from GET or POST
    $class = '\App\Models\\' . request('modelName');
    $model = $class::findOrFail(request('modelId'));

    return $model;

}
