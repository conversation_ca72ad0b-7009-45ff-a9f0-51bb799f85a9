<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use App\Observers\CommentObserver;

#[ObservedBy(CommentObserver::class)]
class Comment extends Model
{
    /** @use HasFactory<\Database\Factories\CommentFactory> */
    use HasFactory;

    protected $with = ['user', 'replyUser'];

    protected $fillable = ['content', 'comment_id', 'reply_user_id'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function replyUser()
    {
        return $this->belongsTo(User::class, 'reply_user_id');
    }

    public function replies()
    {
        return $this->hasMany(Comment::class, 'comment_id');
    }
}
