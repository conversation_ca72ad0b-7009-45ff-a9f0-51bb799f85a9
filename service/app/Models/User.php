<?php
namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'mobile',
        'nickname',
        'sex',
        'home',
        'weibo',
        'github',
        'wechat',
        'qq',
        'avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'openid',
        'unionid',
        'email',
        'mobile',
        'real_name'
    ];

    protected $appends = [
        'is_admin',
    ];

    // computed attributes
    // protected function isAdmin(): Attribute
    // {
    //     return Attribute::make(
    //         get: fn() => $this->id == 1,
    //     );
    // }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    protected function isAdmin(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->id == 1,
        );
    }

    public function topics(): HasMany
    {
        return $this->hasMany(Topic::class);
    }

    public function signs(): HasMany
    {
        return $this->hasMany(Sign::class);
    }
}
