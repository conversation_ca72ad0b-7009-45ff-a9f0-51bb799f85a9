<?php

namespace App\Observers;

use App\Models\Sign;
use App\Models\SignCount;

class SignObserver
{

    private function updateSignCount(Sign $sign): void
    {
        $user = $sign->user;
        SignCount::updateOrCreate(
            ['user_id' => $user->id],
            [
                'year' => $user->signs()->whereYear('created_at', now()->year)->count(),
                'month' => $user->signs()->where<PERSON><PERSON><PERSON>('created_at', now()->month)->whereYear('created_at', now()->year)->count(),
                'total' => $user->signs()->count(),
            ]
        );
    }

    /**
     * Handle the Sign "created" event.
     */
    public function created(Sign $sign): void
    {
        $this->updateSignCount($sign);
    }

    /**
     * Handle the Sign "updated" event.
     */
    public function updated(Sign $sign): void
    {

    }

    /**
     * Handle the Sign "deleted" event.
     */
    public function deleted(Sign $sign): void
    {
        $this->updateSignCount($sign);
    }

    /**
     * Handle the Sign "restored" event.
     */
    public function restored(Sign $sign): void
    {
        //
    }

    /**
     * Handle the Sign "force deleted" event.
     */
    public function forceDeleted(Sign $sign): void
    {
        //
    }
}
