<?php
namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Parsedown;

class TopicResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $Parsedown = new Parsedown();
        $content   = $Parsedown->text($this->content);

        return [ ...parent::toArray($request), 'html' => $content];
    }
}
