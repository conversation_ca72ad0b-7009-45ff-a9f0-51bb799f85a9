<?php
namespace App\Http\Controllers;

use App\Http\Requests\StoreCommentRequest;
use App\Http\Resources\CommentResource;
use App\Models\Comment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class CommentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $model = getModel();

        return CommentResource::collection($model->comments()->whereNull('comment_id')->with(['replies'])->get());
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCommentRequest $request, Comment $comment)
    {
        $comment->user_id = Auth::id();
        $comment->fill($request->input());

        // a helper function to get the model from the request
        $model = getModel();
        $model->comments()->save($comment);

        return new CommentResource($comment);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Comment $comment)
    {
        Gate::authorize('delete', $comment);
        $comment->delete();

        return response()->noContent();
    }
}
