<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSignRequest;
use App\Http\Requests\UpdateSignRequest;
use App\Http\Resources\SignResource;
use App\Models\Sign;
use App\Models\User;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class SignController extends Controller implements HasMiddleware
{

    public static function middleware(): array
    {
        return [
            new Middleware('auth:sanctum', null, ['index']),
        ];
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // only fetch today's sign
        $signs = Sign::whereDate('created_at', now())->get();
        return SignResource::collection($signs);
    }

    public function userSignList(User $user)
    {

        return SignResource::collection($user->signs()->paginate(10));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSignRequest $request, Sign $sign)
    {
        Gate::authorize('create', Sign::class);
        $sign->fill($request->input());
        $sign->user_id = Auth::id();
        $sign->save();
        return new SignResource($sign);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Sign $sign)
    {
        Gate::authorize('delete', $sign);
        $sign->delete();
        return $this->respondNoContent();
    }
}
