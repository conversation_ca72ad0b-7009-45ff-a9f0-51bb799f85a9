<?php
namespace App\Http\Controllers;

use App\Http\Requests\StoreUserRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    public function current()
    {
        if (Auth::check()) {
            return new UserResource(Auth::user());
        }
    }

    public function info(User $user)
    {
        return new UserResource($user);
    }

    public function update(StoreUserRequest $request)
    {
        $user = Auth::user();
        $user->fill($request->input());
        $user->save();

        return new UserResource($user);
    }

    public function password(Request $request)
    {
        $user = Auth::user();
        $request->validate([
            'oldPassword'           => [
                'required',
                function ($attribute, $value, $fail) use ($user) {
                    if (! Hash::check($value, $user->password)) {
                        $fail('The old password is incorrect.');
                    }
                },
            ],
            'password'              => ['required', 'confirmed'],
            'password_confirmation' => ['required'],
        ], [], [
            'oldPassword'           => 'Old Password',
            'password'              => 'New Password',
            'password_confirmation' => 'Confirm Password',
        ]);

        $user->password = Hash::make($request->input('password'));
        $user->save();

        return new UserResource($user);
    }

    public function uploadAvatar(Request $request)
    {
        app('image')->scale($request->file('file'), 300, 300);

        return app('upload')->run($request->file('file'), 'local');
    }

    public function sendBindCode(Request $request, string $field, User $user)
    {
        $rule = $field == 'email' ? ['required', 'email'] : ['required', 'regex:/^\d{11}$/'];

        $request->validate([
            $field => $rule,
        ]);

        // 临时创建一个空的用户，用于发送验证码
        $user->$field = request($field);

        app('code')->send($user, $field);

        return response()->json(['message' => 'Code sent.']);
    }

    public function bindEmail(Request $request)
    {
        $request->validate([
            'email' => ['required', 'email'],
            'code'  => ['required', function ($attribute, $value, $fail) use ($request) {
                if ($value != Cache::get('send_code')) {
                    $fail('The code is incorrect.');
                }
            }],
        ]);

        $user        = Auth::user();
        $user->email = $request->input('email');
        $user->save();

        return $this->respondOk('Bound successfully.');
    }

    public function bindMobile(Request $request)
    {
        $request->validate([
            'mobile' => ['required', 'regex:/^\d{11}$/'],
            'code'   => ['required', function ($attribute, $value, $fail) use ($request) {
                if ($value !== Cache::get('send_code')) {
                    $fail('The code is incorrect.');
                }
            }],
        ]);

        $user         = Auth::user();
        $user->mobile = $request->input('mobile');
        $user->save();

        return $this->respondOk('Bound successfully.');
    }
}
