<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;

class UploadController extends Controller
{

    public function image(Request $request)
    {
        // $file = $request->file('file');
        // // save into public/attachments/yearmonth/random_filename.ext
        // $url = $file->storeAs('attachments/' . date('Ym'), $file->hashName(), 'public');

        // return ['url' => url($url)];

        // 使用定义服务的方法
        return app('upload')->run($request->file('file'), 'local');
    }
}
