<?php
namespace App\Http\Controllers;

use App\Http\Requests\RegisterRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    public function register(RegisterRequest $request, User $user)
    {
        $user->name     = $request->input('name');
        $user->password = Hash::make($request->input('password'));
        $user->save();

        return ['user' => $user->refresh()];
    }

    // mobile email or username
    public function login(Request $request, User $user)
    {
        $field = preg_match('/^\d{11}$/', $request->input('account')) ? 'mobile' : (filter_var($request->input('account'), FILTER_VALIDATE_EMAIL) ? 'email' : 'name');

        $request->validate([
            'account'  => ['required', 'exists:users,' . $field],
            'password' => ['required'],
            'captcha'  => ['required', 'captcha'],
        ], ['captcha.captcha' => 'The captcha is incorrect.'], [
            'account'  => 'Account',
            'password' => 'Password',
            'captcha'  => 'Captcha',
        ]);

        $user = User::where($field, $request->input('account'))->first();

        if (! Hash::check($request->input('password'), $user->password)) {
            throw ValidationException::withMessages([
                'password' => ['The provided password is incorrect.'],
            ]);
        }

        // saved logged in session
        Auth::login($user);

        return ['user' => $user->refresh()];
    }

    public function logout(Request $request)
    {
        Auth::logout();
        return ['message' => 'Logged out successfully'];
    }

    public function sendCode(Request $request, string $field, User $user)
    {
        $rule = $field == 'email' ? ['required', 'email'] : ['required', 'regex:/^\d{11}$/'];

        $request->validate([
            $field => [ ...$rule, 'exists:users,' . $field], // has to be existing in db
        ]);

        // 临时创建一个空的用户，用于发送验证码
        $user->$field = request($field);

        app('code')->send($user, $field);

        return response()->json(['message' => 'Code sent.']);
    }

    public function forget(Request $request, string $field)
    {
        $rule = $field == 'email' ? ['required', 'email'] : ['required', 'regex:/^\d{11}$/'];

        $request->validate([
            'account'  => [ ...$rule, 'exists:users,' . $field],
            'code'     => ['required', function ($attribute, $value, $fail) use ($request) {
                if ($value != Cache::get('send_code')) {
                    $fail('The code is incorrect.');
                }
            },
            ],
            'captcha'  => ['required', 'captcha'],
            'password' => ['required', 'confirmed'],
        ], ['captcha.captcha' => 'The captcha is incorrect.'], ['account' => $field == 'email' ? 'Email' : 'Mobile']);

        $user           = User::where($field, $request->input($field))->first();
        $user->password = Hash::make($request->input('password'));
        $user->save();

        return $this->respondOk('Reset password successfully.');

    }
}
