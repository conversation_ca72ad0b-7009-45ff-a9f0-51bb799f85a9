<?php
namespace App\Http\Controllers;

use App\Http\Requests\StoreVideoRequest;
use App\Http\Requests\UpdateVideoRequest;
use App\Http\Resources\VideoResource;
use App\Models\Video;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\Gate;

class VideoController extends Controller implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            new Middleware('auth:sanctum', null, ['index']),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return VideoResource::collection(Video::paginate(10)->withPath('')->onEachSide(1));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreVideoRequest $request, Video $video)
    {
        Gate::authorize('create', Video::class);

        // $video->fill($request->input());

        // $video->save();

        // return new VideoResource($video);
    }

    /**
     * Display the specified resource.
     */
    public function show(Video $video)
    {
        return new VideoResource($video->load(['chapter.videos', 'lesson.chapters']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateVideoRequest $request, Video $video)
    {
        Gate::authorize('update', $video);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Video $video)
    {

    }
}
