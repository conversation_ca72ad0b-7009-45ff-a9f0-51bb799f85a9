<?php
namespace App\Http\Controllers;

use App\Http\Requests\StoreTopicRequest;
use App\Http\Requests\UpdateTopicRequest;
use App\Http\Resources\TopicResource;
use App\Models\Topic;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class TopicController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $topics = Topic::when(request('uid'), function (Builder $query) {
            $query->where('user_id', request('uid'));
        })->paginate(10);

        return TopicResource::collection($topics->withPath(''));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTopicRequest $request, Topic $topic)
    {
        $topic->fill($request->input());
        $topic->user_id = Auth::id();
        $topic->save();

        return new TopicResource($topic);
    }

    /**
     * Display the specified resource.
     */
    public function show(Topic $topic)
    {
        return new TopicResource($topic);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTopicRequest $request, Topic $topic)
    {
        Gate::authorize('update', $topic);
        $topic->fill($request->input());
        $topic->save();

        return new TopicResource($topic);

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Topic $topic)
    {
        Gate::authorize('delete', $topic);
        $topic->delete();

        return response()->noContent();
    }
}
