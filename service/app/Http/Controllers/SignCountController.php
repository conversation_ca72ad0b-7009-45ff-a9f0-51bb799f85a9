<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSignCountRequest;
use App\Http\Requests\UpdateSignCountRequest;
use App\Models\SignCount;

class SignCountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSignCountRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(SignCount $signCount)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SignCount $signCount)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSignCountRequest $request, SignCount $signCount)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SignCount $signCount)
    {
        //
    }
}
