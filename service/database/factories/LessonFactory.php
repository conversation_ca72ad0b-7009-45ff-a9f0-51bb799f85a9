<?php
namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Lesson>
 */
class LessonFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            "title"       => fake()->sentence(),
            'preview'     => fake()->imageUrl(),
            'description' => fake()->sentences(5, true),
            'price'       => fake()->numberBetween(100, 999),
        ];
    }
}
