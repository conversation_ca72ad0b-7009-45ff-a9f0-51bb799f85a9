<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete()->comment('Commenter');
            $table->foreignId('reply_user_id')->nullable()->constrained('users')->cascadeOnDelete()->comment('Reply to comment user id');
            $table->foreignId('comment_id')->nullable()->constrained('comments')->cascadeOnDelete()->comment('Reply to comment id');
            $table->text('content')->comment('Comment content');
            // 定义多态是因为一个评论可以属于多个模型，比如一个评论可以属于一个帖子，也可以属于一个视频
            $table->morphs('commentable');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comments');
    }
};
