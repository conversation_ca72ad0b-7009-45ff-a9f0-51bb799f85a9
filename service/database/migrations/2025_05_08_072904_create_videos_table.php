<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('videos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lesson_id')->nullable()->constrained('lessons')->cascadeOnDelete()->comment('Belonged Lesson');
            $table->foreignId('chapter_id')->constrained('chapters')->cascadeOnDelete()->comment('Belonged Chapter');
            $table->string('title')->nullable()->comment('Video Title');
            $table->string('path', 500)->nullable()->comment('Video Path');
            $table->unsignedSmallInteger('order')->default(0)->comment('Order');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('videos');
    }
};
