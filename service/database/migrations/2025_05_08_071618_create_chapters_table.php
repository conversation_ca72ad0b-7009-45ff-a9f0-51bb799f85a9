<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chapters', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lesson_id')->constrained('lessons')->cascadeOnDelete();
            $table->string('title')->comment('Chapter Title');
            $table->string('preview')->comment('Preview Image');
            $table->text('description')->nullable()->comment('Chapter Description');
            $table->unsignedInteger('video_num')->default(0)->comment('Video Number');
            $table->unsignedSmallInteger('order')->default(0)->comment('Order');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chapters');
    }
};
