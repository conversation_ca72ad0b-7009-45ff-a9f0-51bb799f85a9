<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable()->unique()->comment('Name');
            $table->string('password')->nullable();
            $table->string('nickname')->nullable()->comment('Nickname');
            $table->string('email')->nullable()->unique();
            $table->string('mobile')->nullable()->unique();
            $table->tinyInteger('sex')->default(1)->comment('Sex');
            $table->string('address')->nullable()->comment('Address');
            $table->string('real_name')->nullable()->comment('Real Name');
            $table->string('avatar')->nullable()->comment('Avatar');
            $table->string('home')->nullable()->comment('Home');
            $table->string('weibo')->nullable()->comment('Weibo');
            $table->string('wechat')->nullable()->comment('Wechat');
            $table->string('github')->nullable()->comment('Github');
            $table->string('qq')->nullable()->comment('QQ');
            // for wechat login
            $table->string('openid')->nullable()->unique()->comment('Openid');
            $table->string('unionid')->nullable()->unique()->comment('Unionid');
            $table->timestamp('email_verified_at')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
