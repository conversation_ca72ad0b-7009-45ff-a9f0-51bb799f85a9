<?php
namespace Database\Seeders;

use App\Models\Chapter;
use App\Models\Lesson;
use Illuminate\Database\Seeder;

class LessonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Lesson::factory(24)->has(Chapter::factory(6)->hasVideos(20, function (array $attributes, Chapter $chapter) {
            return ['lesson_id' => $chapter->lesson_id];
        }))->create();

        Lesson::limit(12)->each(function (Lesson $lesson, $index) {
            $lesson->type    = 'system';
            $lesson->preview = url('/assets/images/lesson/' . ($index + 1) . '.jpeg');
            $lesson->save();
        });

        Lesson::offset(12)->limit(12)->each(function (Lesson $lesson, $index) {
            $lesson->type    = 'project';
            $lesson->preview = url('/assets/images/project/' . ($index + 1) . '.jpeg');
            $lesson->save();
        });
    }
}
