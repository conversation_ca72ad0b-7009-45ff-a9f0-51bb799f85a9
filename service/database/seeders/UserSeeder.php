<?php
namespace Database\Seeders;

use App\Models\Topic;
use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::factory(10)
            ->hasSigns(1)
            ->has(Topic::factory(5)->hasComments(3, function ($attributes, Topic $topic) {
                return [
                    'user_id' => $topic->user_id,
                ];
            }))->create();
        $user        = User::find(1);
        $user->name  = 'admin';
        $user->email = '<EMAIL>';
        $user->save();
        $user       = User::find(2);
        $user->name = 'test user';
        $user->save();

    }
}
