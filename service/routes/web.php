<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\ChapterController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\LessonController;
use App\Http\Controllers\SignController;
use App\Http\Controllers\TopicController;
use App\Http\Controllers\UploadController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\VideoController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return 'ok';
});

Route::prefix('api')->group(function () {
    Route::apiResource('lesson', LessonController::class);
    Route::apiResource('chapter', ChapterController::class);
    Route::apiResource('video', VideoController::class);

    // auth
    Route::post('/auth/register', [AuthController::class, 'register']);
    Route::post('/auth/login', [AuthController::class, 'login']);
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::post('/auth/send-code/{field}', [AuthController::class, 'sendCode']);
    Route::post('/auth/forget-password/{field}', [AuthController::class, 'forget']);

    // user
    Route::get('/user/current', [UserController::class, 'current']);
    Route::get('/user/info/{user}', [UserController::class, 'info']);
    Route::put('/user', [UserController::class, 'update']);
    Route::put('/user/password', [UserController::class, 'password']);
    Route::post('/user/upload/avatar', [UserController::class, 'uploadAvatar']);
    Route::post('/user/send-code/{field}', [UserController::class, 'sendBindCode']);
    Route::post('/user/bind/email', [UserController::class, 'bindEmail']);
    Route::post('/user/bind/mobile', [UserController::class, 'bindMobile']);

    // topic
    Route::apiResource('topic', TopicController::class);

    // upload file
    Route::post('/upload/image', [UploadController::class, 'image']);

    // comment
    Route::apiResource('comment', CommentController::class);

    // sign
    Route::apiResource('sign', SignController::class);
    Route::get('/sign/userSignList/{user}', [SignController::class, 'userSignList']);
});
