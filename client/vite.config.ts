import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tailwindcss from '@tailwindcss/vite';
import path from 'path';
import { TanStackRouterVite } from '@tanstack/router-plugin/vite';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    TanStackRouterVite({
      target: 'react',
      autoCodeSplitting: true,
      routesDirectory: './src/pages',
      generatedRouteTree: './src/routeTree.gen.ts',
      routeFileIgnorePrefix: '-',
      quoteStyle: 'single',
    }),
    react(),
    tailwindcss(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://online-study-platform.test',
        changeOrigin: true,
      },
      '/images': {
        target: 'http://online-study-platform.test',
        changeOrigin: true,
      },
      '/assets': {
        target: 'http://online-study-platform.test',
        changeOrigin: true,
      },
      '/captcha': {
        target: 'http://online-study-platform.test',
        changeOrigin: true,
      },
    },
  },
});
