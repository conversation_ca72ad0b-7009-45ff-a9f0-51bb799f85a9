import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: { retry: false },
  },
});

export const useAppQuery = () => {
  const AppQueryProvider = ({ children }: { children: React.ReactNode }) => (
    // Provide the client to your App
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  return {
    AppQueryProvider,
  };
};
