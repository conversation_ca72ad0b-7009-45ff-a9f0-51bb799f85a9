import type { IUser } from '@/type/user';

const data = {
  user: {},
} as {
  user: IUser;
};

export const useAuth = () => {
  // 使用泛型 T 来约束 field 参数的类型
  // T extends keyof IUser 确保 field 只能是 IUser 接口中定义的属性名
  // 这样可以：
  // 1. 保证类型安全，防止访问不存在的属性
  // 2. 在使用时获得准确的类型推断
  // 3. 保持与 IUser 接口的类型一致性
  const user = <T extends keyof IUser>(field: T) => {
    return data.user[field];
  };

  const getUserInfo = () => {
    return data.user;
  };

  const isAdmin = () => {
    return data.user.id === 1;
  };

  const setUser = (user: IUser) => {
    data.user = user;
  };

  const isAuthenticated = () => {
    const isLogin = !!data.user?.id;

    // 如果未登录，则将当前页面地址存储到 localStorage 中
    if (!isLogin) {
      localStorage.setItem('redirectUrl', location.href);
    }

    return isLogin;
  };

  return {
    user,
    getUserInfo,
    setUser,
    isAuthenticated,
    isAdmin,
  };
};

// export useAuth hook return type dynamically based on the return type of the useAuth hook
export type IUserAuth = ReturnType<typeof useAuth>;
