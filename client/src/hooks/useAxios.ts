import axios, { AxiosError } from 'axios';
import { useValidateStore } from '@/store/useValidateStore';
import { toast } from 'sonner';
export const useAxios = () => {
  const setErrors = useValidateStore((state) => state.setErrors);
  const clearErrors = useValidateStore((state) => state.clearErrors);
  const axiosInstance = axios.create({
    baseURL: '/api',
    timeout: 10000,
    // header: {}
  });

  // Add a request interceptor
  axiosInstance.interceptors.request.use(
    function (config) {
      // Do something before request is sent

      // clear errors
      clearErrors();
      return config;
    },
    function (error) {
      // Do something with request error
      return Promise.reject(error);
    }
  );

  // Add a response interceptor
  axiosInstance.interceptors.response.use(
    function (response) {
      // Any status code that lie within the range of 2xx cause this function to trigger
      // Do something with response data
      return response;
    },
    function (error: AxiosError) {
      console.log('error', error);
      const data = error.response?.data as {
        errors: Record<string, string[]>;
        message?: string;
      };
      const message = data?.message || '';
      switch (error.status) {
        case 422: {
          setErrors(data.errors);
          break;
        }
        case 403: {
          toast('You are not authorized to access');
          break;
        }
        case 429: {
          toast(message || 'You are sending requests too frequently');
          break;
        }
        default: {
          break;
        }
      }
      // Any status codes that falls outside the range of 2xx cause this function to trigger
      // Do something with response error
      return Promise.reject(error);
    }
  );

  return {
    axiosInstance,
  };
};
