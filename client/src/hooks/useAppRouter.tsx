import { RouterProvider, createRouter } from '@tanstack/react-router';

// Import the generated route tree
import { routeTree } from '../routeTree.gen';
import { useAuth } from './useAuth';

// Create a new router instance
const router = createRouter({
  routeTree,
  context: {
    auth: undefined!, // set router context which can be used in all routes
  },
});

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

// useAppRouter provides a provider with custom context
export const useAppRouter = () => {
  const auth = useAuth();
  const AppRouterProvider = () => (
    // inject the custom context into the router context
    <RouterProvider
      router={router}
      context={{ auth }}
    />
  );

  return {
    AppRouterProvider,
  };
};
