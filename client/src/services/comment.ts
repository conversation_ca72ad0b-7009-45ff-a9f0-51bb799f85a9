import { useAxios } from '@/hooks/useAxios';
import type { IComment } from '@/type/comment';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import qs from 'qs';

// get comments
export const useGetCommentListQuery = (params: {
  modelName: string;
  modelId: number;
}) => {
  const { axiosInstance } = useAxios();

  return useQuery<IComment[], AxiosError>({
    queryKey: ['useGetCommentListQuery', ...Object.values(params)],
    queryFn: async () => {
      const res = await axiosInstance.get(`/comment?` + qs.stringify(params));

      return res.data;
    },
  });
};

// post comment
export const usePostCommentMutation = () => {
  const { axiosInstance } = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (comment: {
      modelName: string;
      modelId: number;
      content: string;
    }) => {
      const res = await axiosInstance.post('/comment', comment);
      queryClient.invalidateQueries({
        queryKey: ['useGetCommentListQuery'],
        stale: true,
      });
      return res.data;
    },
  });
};

// delete comment
export const useDeleteCommentMutation = () => {
  const { axiosInstance } = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) => {
      const res = await axiosInstance.delete(`/comment/${id}`);
      queryClient.invalidateQueries({
        queryKey: ['useGetCommentListQuery'],
        stale: true,
      });
      return res.data;
    },
  });
};
