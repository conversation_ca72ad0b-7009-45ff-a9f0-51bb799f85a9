import { useAxios } from '@/hooks/useAxios';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useNavigate } from '@tanstack/react-router';

// login
export const useLoginMutation = () => {
  const { axiosInstance } = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { account: string; password: string }) => {
      const response = await axiosInstance.post('/auth/login', data);
      return response.data;
    },
    onSuccess: () => {
      toast.success('Login Success', { position: 'top-center' });
      // clear cache
      queryClient.invalidateQueries({
        queryKey: ['useGetCurrentUserQuery'],
      });

      // if redirectUrl is set, navigate to it. Otherwise, navigate to home page
      const url = localStorage.getItem('redirectUrl') || '/';
      location.href = url;
    },
    onError: (error) => {
      console.log(error);
      toast.error('Login Failed', { position: 'top-center' });
    },
  });
};

// logout
export const useLogoutMutation = () => {
  const { axiosInstance } = useAxios();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const response = await axiosInstance.post('/auth/logout');
      return response.data;
    },
    onSuccess: () => {
      toast.info('Logout Success', { position: 'top-center' });
      // clear cache
      queryClient.invalidateQueries({ queryKey: ['useGetCurrentUserQuery'] });
      navigate({ to: '/' });
    },
    onError: (error) => {
      console.log(error);
      toast.error('Logout Failed', { position: 'top-center' });
    },
  });
};

// register
export const useRegisterMutation = <T>() => {
  const { axiosInstance } = useAxios();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async (data: T) => {
      const response = await axiosInstance.post('/auth/register', data);
      return response.data;
    },
    onSuccess: () => {
      toast.success('Register Success', { position: 'top-center' });
      navigate({ to: '/auth/login' });
    },
    onError: (error) => {
      console.log(error);
      toast.error('Register Failed', { position: 'top-center' });
    },
  });
};

// forget password
export const useForgetPasswordMutation = () => {
  const { axiosInstance } = useAxios();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async (data) => {
      const response = await axiosInstance.post(
        '/auth/forget-password/' + data.fieldType,
        data
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success('Reset Password Success', { position: 'top-center' });
      navigate({ to: '/auth/login' });
    },
    onError: (error) => {
      console.log(error);
      toast.error('Reset Password Failed', { position: 'top-center' });
    },
  });
};
