import { useAxios } from '@/hooks/useAxios';
import type { IUser } from '@/type/user';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import { toast } from 'sonner';

export const useGetCurrentUserQuery = () => {
  const { axiosInstance } = useAxios();
  return useQuery<IUser, AxiosError>({
    queryKey: ['useGetCurrentUserQuery'],
    queryFn: async () => {
      const res = await axiosInstance.get('/user/current');

      return res.data;
    },
  });
};

export const useGetUserInfoQuery = (id: number) => {
  const { axiosInstance } = useAxios();
  return useQuery<IUser, AxiosError>({
    queryKey: ['useGetUserInfoQuery', id],
    queryFn: async () => {
      const res = await axiosInstance.get(`/user/info/${id}`);

      return res.data;
    },
  });
};

// update user profile
export const useUpdateUserProfileMutation = () => {
  const { axiosInstance } = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: IUser) => {
      const res = await axiosInstance.put('/user', data);

      return res.data;
    },
    onSuccess: () => {
      toast('Updated successfully');

      queryClient.invalidateQueries({ queryKey: ['useGetCurrentUserQuery'] });
    },
  });
};

// update user password
export const useUpdateUserPasswordMutation = <T>() => {
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationFn: async (data: T) => {
      const res = await axiosInstance.put('/user/password', data);

      return res.data;
    },
    onSuccess: () => {
      toast('Updated successfully');
    },
  });
};

// bind user email
export const useBindUserEmailMutation = <T>() => {
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationFn: async (data: T) => {
      const res = await axiosInstance.post('/user/bind/email', data);

      return res.data;
    },
    onSuccess: () => {
      toast('Bound successfully');
    },
  });
};

// bind user mobile
export const useBindUserMobileMutation = <T>() => {
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationFn: async (data: T) => {
      const res = await axiosInstance.post('/user/bind/mobile', data);

      return res.data;
    },
    onSuccess: () => {
      toast('Bound successfully');
    },
  });
};
