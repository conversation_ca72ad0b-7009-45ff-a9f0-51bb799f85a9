import { useAxios } from '@/hooks/useAxios';
import type { IPaginate } from '@/type/paginate';
import type { ISign } from '@/type/sign';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import { toast } from 'sonner';

// get today's sign
export const useGetSignListQuery = () => {
  const { axiosInstance } = useAxios();
  return useQuery<ISign[], AxiosError>({
    queryKey: ['useGetSignListQuery'],
    queryFn: async () => {
      const res = await axiosInstance.get('/sign');

      return res.data;
    },
  });
};

// post sign
export const usePostSignMutation = () => {
  const { axiosInstance } = useAxios();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: Pick<ISign, 'content' | 'mood'>) => {
      const res = await axiosInstance.post('sign', data);

      queryClient.invalidateQueries({
        queryKey: ['useGetSignListQuery'],
        stale: true,
      });

      return res.data;
    },
  });
};

// delete sign
export const useDeleteSignMutation = () => {
  const { axiosInstance } = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) => {
      await axiosInstance.delete(`sign/${id}`);

      queryClient.invalidateQueries({
        queryKey: ['useGetSignListQuery'],
        stale: true,
      });
    },
    onSuccess: () => {
      toast.success('Sign deleted successfully');
    },
  });
};

// get user sign list
export const useGetUserSignListQuery = (userId: number) => {
  const { axiosInstance } = useAxios();
  return useQuery<IPaginate<ISign>, AxiosError>({
    queryKey: ['useGetUserSignListQuery', userId],
    queryFn: async () => {
      const res = await axiosInstance.get(`/sign/userSignList/${userId}`);

      return res.data;
    },
  });
};
