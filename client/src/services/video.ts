import { useAxios } from '@/hooks/useAxios';
import type { IPaginate } from '@/type/paginate';
import type { IVideo } from '@/type/video';
import { useQuery } from '@tanstack/react-query';
import type { AxiosError } from 'axios';

// get video by id
export const useGetVideoDetailQuery = (id: number) => {
  const { axiosInstance } = useAxios();

  return useQuery<IVideo, AxiosError>({
    queryKey: ['useGetVideoDetailQuery', id],
    queryFn: async () => {
      const response = await axiosInstance.get(`/video/${id}`);

      return response.data;
    },
  });
};

// get video
export const useGetVideoListQuery = (page: number) => {
  const { axiosInstance } = useAxios();

  return useQuery<IPaginate<IVideo>, AxiosError>({
    queryKey: ['useGetVideoListQuery', page],
    queryFn: async () => {
      const response = await axiosInstance.get(`/video?page=${page}`);

      return response.data;
    },
  });
};
