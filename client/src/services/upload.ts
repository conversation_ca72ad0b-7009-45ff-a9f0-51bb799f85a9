import { useAxios } from '@/hooks/useAxios';
import { useMutation } from '@tanstack/react-query';

// upload image
export const useUploadImageMutation = () => {
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      const res = await axiosInstance.post('/upload/image', formData);

      return res.data;
    },
  });
};
