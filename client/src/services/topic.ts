import { useAxios } from '@/hooks/useAxios';
import type { IPaginate } from '@/type/paginate';
import type { ITopic } from '@/type/topic';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import type { AxiosError } from 'axios';
import qs from 'qs';
import { toast } from 'sonner';

// get topic list
export const useGetTopicListQuery = (params: {
  page: number;
  uid?: number;
}) => {
  const { axiosInstance } = useAxios();

  return useQuery<IPaginate<ITopic>, AxiosError>({
    queryKey: ['useGetTopicListQuery', ...Object.values(params)],
    queryFn: async () => {
      const res = await axiosInstance.get(`/topic?` + qs.stringify(params));

      return res.data;
    },
  });
};

// submit topic
export const useSubmitTopicMutation = () => {
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationFn: async (data: Partial<ITopic>) => {
      const res = await axiosInstance.post('/topic', data);

      return res.data;
    },
  });
};

// get topic
export const useGetTopicQuery = (id: number) => {
  const { axiosInstance } = useAxios();

  return useQuery<ITopic, AxiosError>({
    queryKey: ['useGetTopicQuery', id],
    queryFn: async () => {
      const res = await axiosInstance.get(`/topic/${id}`);

      return res.data;
    },
  });
};

// update topic
export const useUpdateTopicMutation = () => {
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationFn: async (data: Partial<ITopic>) => {
      const res = await axiosInstance.put(`/topic/${data.id}`, data);

      return res.data;
    },
  });
};

// delete topic
export const useDeleteTopicMutation = () => {
  const { axiosInstance } = useAxios();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async (id: number) => {
      const res = await axiosInstance.delete(`/topic/${id}`);

      return res.data;
    },
    onSuccess: () => {
      toast.success('Topic deleted successfully');
      navigate({
        to: '/topic',
      });
    },
  });
};
