import type { lessonTitle } from '@/config/app';
import { useAxios } from '@/hooks/useAxios';
import type { ILesson } from '@/type/lesson';
import type { IPaginate } from '@/type/paginate';
import { useQuery } from '@tanstack/react-query';
import type { AxiosError } from 'axios';

// get lesson list
export const useGetLessonListQuery = (
  type: keyof typeof lessonTitle,
  page: number
) => {
  const { axiosInstance } = useAxios();

  return useQuery<IPaginate<ILesson>, AxiosError>({
    queryKey: ['useGetLessonListQuery', type, page],
    queryFn: async () => {
      const res = await axiosInstance.get(`/lesson?type=${type}&page=${page}`);

      return res.data;
    },
  });
};

// get lesson detail
export const useGetLessonDetailQuery = (id: number) => {
  const { axiosInstance } = useAxios();

  return useQuery<ILesson, AxiosError>({
    queryKey: ['useGetLessonDetailQuery', id],
    queryFn: async () => {
      const res = await axiosInstance.get(`/lesson/${id}`);

      return res.data;
    },
  });
};
