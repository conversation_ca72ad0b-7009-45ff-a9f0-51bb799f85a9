import { useAxios } from '@/hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import type { IChapter } from '@/type/chapter';
import type { AxiosError } from 'axios';
import type { IPaginate } from '@/type/paginate';

// get chapter by id
export const useGetChapterQuery = (id: number) => {
  const { axiosInstance } = useAxios();

  return useQuery<IChapter, AxiosError>({
    queryKey: ['chapter', id],
    queryFn: async () => {
      const res = await axiosInstance.get(`/chapter/${id}`);

      return res.data;
    },
  });
};

// get chapter list
export const useGetChapterListQuery = (page: number) => {
  const { axiosInstance } = useAxios();

  return useQuery<IPaginate<IChapter>, AxiosError>({
    queryKey: ['useGetChapterListQuery', page],
    queryFn: async () => {
      const res = await axiosInstance.get(`/chapter?page=${page}`);

      return res.data;
    },
  });
};
