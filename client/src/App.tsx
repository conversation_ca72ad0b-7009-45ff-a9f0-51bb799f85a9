import { useAppRouter } from '@/hooks/useAppRouter';
import { useAppQuery } from './hooks/useAppQuery';
import { useGetCurrentUserQuery } from './services/user';
import { Toaster } from 'sonner';
import { useAuth } from './hooks/useAuth';
import Loading from './components/loading/Loading';
import Error from './components/error/Error';

function App() {
  const { AppQueryProvider } = useAppQuery();

  return (
    <AppQueryProvider>
      <InitData />
      <Toaster
        position='top-center'
        toastOptions={{
          duration: 1000,
        }}
      />
    </AppQueryProvider>
  );
}

function InitData() {
  const { AppRouterProvider } = useAppRouter();
  const { isPending, isError, error, data } = useGetCurrentUserQuery();

  const { setUser } = useAuth();

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  setUser(data);

  return <AppRouterProvider />;
}

export default App;
