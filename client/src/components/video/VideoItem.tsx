import { cn } from '@/lib/utils';
import type { IVideo } from '@/type/video';
import { VideoTwo } from '@icon-park/react';
import { Link } from '@tanstack/react-router';
import dayjs from 'dayjs';
import type { FC } from 'react';

interface Props {
  video: IVideo;
  showTime?: boolean;
  className?: string;
}

const VideoItem: FC<Props> = ({ video, showTime = true, className }) => {
  return (
    <main
      className={cn(
        'flex items-center justify-between border-b py-2',
        className
      )}
    >
      <Link
        key={video.id}
        to={`/video/$id`}
        params={{ id: video.id }}
        className='flex items-center gap-2 font-light'
      >
        <VideoTwo
          theme='outline'
          size='12'
          fill='#333'
        />
        <div className='line-clamp-1'>{video.title}</div>
      </Link>
      {showTime && (
        <div className='text-xs font-light'>
          {dayjs(video.created_at).fromNow()}
        </div>
      )}
    </main>
  );
};
export default VideoItem;
