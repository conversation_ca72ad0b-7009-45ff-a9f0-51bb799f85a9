import { Link } from '@tanstack/react-router';
import { But<PERSON> } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { useAuth } from '@/hooks/useAuth';
import dayjs from 'dayjs';
import { useLogoutMutation } from '@/services/auth';

const UserDropdown = () => {
  const auth = useAuth();
  const logoutMutation = useLogoutMutation();

  if (!auth.isAuthenticated()) {
    return <LoginComponent />;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className='flex items-center gap-2'>
        <img
          src={auth.user('avatar')}
          className='size-8 rounded-lg object-cover'
        />
        <div className='flex flex-col items-start text-xs font-light '>
          {auth.user('nickname')}
          <span className='text-muted-foreground'>
            registered at {dayjs(auth.user('created_at')).fromNow()}
          </span>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuLabel className='flex justify-between items-center'>
          {auth.user('name')} <span>uid: {auth.user('id')}</span>
        </DropdownMenuLabel>
        <DropdownMenuItem>
          <Link to='/member/info'>My Profile</Link>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Link to='/space'>My Space</Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem>Settings</DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            logoutMutation.mutate();
          }}
        >
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserDropdown;

function LoginComponent() {
  return (
    <div className='flex gap-2'>
      <Link to='/auth/login'>
        <Button
          variant='default'
          size='sm'
        >
          登陆
        </Button>
      </Link>
      <Link to='/'>
        <Button
          variant='outline'
          size='sm'
        >
          注册
        </Button>
      </Link>
    </div>
  );
}
