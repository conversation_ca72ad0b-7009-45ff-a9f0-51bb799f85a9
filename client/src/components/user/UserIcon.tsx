import type { IUser } from '@/type/user';
import { Link } from '@tanstack/react-router';
import type { FC } from 'react';

interface UserIconProps {
  user: IUser;
}

const UserIcon: FC<UserIconProps> = ({ user }) => {
  return (
    <Link
      to={'/space/$id'}
      params={{ id: user.id }}
      className='w-10 h-10 rounded-sm overflow-hidden'
    >
      <img
        src={user.avatar}
        alt={user.name}
        className='object-cover w-full h-full hover:scale-130 transition-all duration-500'
      />
    </Link>
  );
};
export default UserIcon;
