import { frontMenus } from '@/config/menu';
import { Link } from '@tanstack/react-router';
import { Rocket } from '@icon-park/react';
import UserDropdown from '../user/UserDropdown';

const FrontNavbar = () => {
  return (
    <main>
      <section className='bg-white h-16 border-t-2 border-t-primary border-b'>
        <div className='m-auto h-16 2xl:w-7xl flex items-center justify-between'>
          <div className='flex items-center gap-6'>
            <div className='flex items-center gap-2 text-primary'>
              <Rocket
                theme='outline'
                size='24'
              />
              在线学习平台
            </div>
            {frontMenus.map((menu) => {
              return (
                <Link
                  to={menu.to}
                  key={menu.title}
                  className='text-sm text-gray-500 hover:text-primary'
                >
                  {menu.title}
                </Link>
              );
            })}
          </div>
          <UserDropdown />
        </div>
      </section>
    </main>
  );
};
export default FrontNavbar;
