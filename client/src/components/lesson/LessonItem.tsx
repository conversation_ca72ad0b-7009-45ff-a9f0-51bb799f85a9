import type { <PERSON>esson } from '@/type/lesson';
import type { FC } from 'react';
import { Badge } from '../ui/badge';
import { Link } from '@tanstack/react-router';
import ChapterDownList from '../chapter/ChapterDownList';

interface Props {
  lesson: ILesson;
}

const LessonItem: FC<Props> = ({ lesson }) => {
  return (
    <main className='bg-background border rounded-lg p-4 shadow-md flex flex-col group'>
      <Link
        to={`/lesson/show/$id`}
        params={{ id: lesson.id }}
        className='cursor-pointer overflow-hidden flex rounded-md'
      >
        <img
          className='duration-300 hover:scale-125'
          src={lesson.preview}
          alt={lesson.title}
        />
      </Link>
      <Link
        to={`/lesson/show/$id`}
        params={{ id: lesson.id }}
        className='pt-3 flex truncate'
      >
        {lesson.title}
      </Link>
      <div className='font-light text-muted-foreground text-xs line-clamp-2 flex-1'>
        <p>{lesson.description}</p>
      </div>
      <div className='pt-3 flex items-center justify-between gap-2 border-t mt-3'>
        <div className='flex items-center gap-2'>
          <Badge variant='outline'>{lesson.video_num}</Badge>
          <span className='text-muted-foreground text-xs'>
            {lesson.video_num > 1 ? 'Chapters' : 'Chapter'}
          </span>
        </div>
        <div className='hidden group-hover:block'>
          <ChapterDownList chapters={lesson.chapters} />
        </div>
        <div className='flex items-center gap-2'>
          <Badge variant='outline'>{lesson.video_num}</Badge>
          <span className='text-muted-foreground text-xs'>
            {lesson.video_num > 1 ? 'Videos' : 'Video'}
          </span>
        </div>
      </div>
    </main>
  );
};
export default LessonItem;
