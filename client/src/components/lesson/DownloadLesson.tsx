import { DownloadOne } from '@icon-park/react';
import { Button, buttonVariants } from '../ui/button';
import type { FC } from 'react';
import type { VariantProps } from 'class-variance-authority';

const DownloadLesson: FC<VariantProps<typeof buttonVariants>> = ({
  variant = 'default',
  size = 'sm',
}) => {
  return (
    <div>
      <Button
        variant={variant}
        size={size}
        className='flex items-center'
      >
        <DownloadOne
          theme='outline'
          size='27'
        />
        HQ Video
      </Button>
    </div>
  );
};
export default DownloadLesson;
