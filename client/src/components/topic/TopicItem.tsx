import type { ITopic } from '@/type/topic';
import { Link } from '@tanstack/react-router';
import dayjs from 'dayjs';
import type { FC } from 'react';
import UserIcon from '../user/UserIcon';

interface Props {
  topic: ITopic;
}

const TopicItem: FC<Props> = ({ topic }) => {
  return (
    <div className='flex items-center gap-2 border-b py-3'>
      <UserIcon user={topic.user} />
      <div className=''>
        <Link
          to={'/topic/$id'}
          params={{ id: topic.id }}
        >
          {topic.title}
        </Link>
        <div className='text-muted-foreground text-xs flex items-center gap-1'>
          <span>{topic.user.name}</span>
          <span>{dayjs(topic.created_at).fromNow()}</span>
        </div>
      </div>
    </div>
  );
};
export default TopicItem;
