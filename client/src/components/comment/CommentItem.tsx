import type { IComment } from '@/type/comment';
import type { FC } from 'react';
import CommentAvatar from './CommentAvatar';
import CommentReply from './CommentReply';
import { cn } from '@/lib/utils';

interface Props {
  comment: IComment;
}

const CommentItem: FC<Props> = ({ comment }) => {
  return (
    <main
      className={cn('border mb-3 rounded-md', {
        'bg-white': !!comment.comment_id,
        'bg-muted': !comment.comment_id,
      })}
    >
      <CommentAvatar comment={comment} />
      <div className='p-3'>
        {comment.content}
        <CommentReply comment={comment} />
      </div>
    </main>
  );
};
export default CommentItem;
