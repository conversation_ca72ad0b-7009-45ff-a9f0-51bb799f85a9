import type { IComment } from '@/type/comment';
import type { FC } from 'react';
import { Textarea } from '../ui/textarea';
import { useCommentContext } from '@/context/CommentContext';
import { Button } from '../ui/button';
import { usePostCommentMutation } from '@/services/comment';
import { useForm } from '@tanstack/react-form';

interface Props {
  comment: IComment;
}

const CommentReply: FC<Props> = ({ comment }) => {
  const { replyId, setReplyId, modelName, modelId } = useCommentContext();
  const postCommentMutation = usePostCommentMutation();

  const form = useForm({
    defaultValues: {
      content: '',
      comment_id: comment.comment_id || comment.id,
      reply_user_id: comment.user_id,
    },
    onSubmit: ({ value }) => {
      postCommentMutation.mutate({
        ...value,
        modelName,
        modelId,
      });
      form.reset();
      setReplyId(0);
    },
  });

  if (replyId !== comment.id) return null;

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <main className='flex flex-col gap-2 items-start mt-4'>
        <form.Field
          name='content'
          children={(field) => (
            <Textarea
              rows={2}
              value={field.state.value}
              onChange={(e) => {
                field.handleChange(e.target.value);
              }}
            />
          )}
        />
        <Button
          variant='outline'
          size='sm'
        >
          Reply
        </Button>
      </main>
    </form>
  );
};
export default CommentReply;
