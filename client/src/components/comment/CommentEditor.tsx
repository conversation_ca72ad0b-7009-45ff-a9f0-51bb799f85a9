import { useForm } from '@tanstack/react-form';
import MarkdownEditor, {
  type MarkdownEditorRef,
} from '../editor/MarkdownEditor';
import { Button } from '../ui/button';
import FieldValidate from '../common/FieldValidate';
import { usePostCommentMutation } from '@/services/comment';
import { useRef, type FC } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Link } from '@tanstack/react-router';
import { useCommentContext } from '@/context/CommentContext';

const CommentEditor: FC = () => {
  const { modelName, modelId } = useCommentContext();
  const postCommentMutation = usePostCommentMutation();
  const auth = useAuth();
  const editorRef = useRef<MarkdownEditorRef | null>(null);
  const form = useForm({
    defaultValues: {
      content: '',
    },
    onSubmit: ({ value }) => {
      postCommentMutation.mutate({
        ...value,
        modelName,
        modelId,
      });
      editorRef.current?.clear();
    },
  });

  if (!auth.isAuthenticated())
    return (
      <div className='text-center w-full flex justify-center'>
        <Link to='/auth/login'>
          <Button variant={'default'}>Login to comment</Button>
        </Link>
      </div>
    );

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
      className='w-full'
    >
      <form.Field
        name='content'
        children={(field) => {
          return (
            <div>
              <MarkdownEditor
                ref={editorRef}
                value={''}
                onChange={field.handleChange}
                className='h-[250px]'
              />
              <FieldValidate
                name='content'
                errors={field.state.meta.errors}
              />
            </div>
          );
        }}
      />
      <Button variant={'default'}>Comment</Button>
    </form>
  );
};
export default CommentEditor;
