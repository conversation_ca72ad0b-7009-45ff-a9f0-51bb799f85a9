import { useGetCommentListQuery } from '@/services/comment';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../ui/card';
import CommentEditor from './CommentEditor';
import { useEffect, type FC } from 'react';
import Loading from '../loading/Loading';
import Error from '../error/Error';
import CommentItem from './CommentItem';
import { CommentProvider, useCommentContext } from '@/context/CommentContext';

interface Props {
  modelName: string;
  modelId: number;
}

const CommentList: FC<Props> = (params) => {
  return (
    <CommentProvider>
      <List {...params} />
    </CommentProvider>
  );
};
export default CommentList;

function List(params: Props) {
  const {
    data: comments,
    isPending,
    isError,
    error,
  } = useGetCommentListQuery(params);
  const { setModelName, setModelId } = useCommentContext();

  useEffect(() => {
    setModelName(params.modelName);
    setModelId(params.modelId);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <Card className='xl:w-7xl mx-auto mt-3'>
      <CardHeader>
        <CardTitle>Comments</CardTitle>
        <CardDescription>Describe your comments here</CardDescription>
      </CardHeader>
      <CardContent>
        {comments.map((comment) => (
          <div
            className='mt-2'
            key={comment.id}
          >
            <CommentItem comment={comment} />
            {comment.replies.length > 0 && (
              <div className='bg-muted p-3 rounded-md mt-2 ml-10 border'>
                {comment.replies.map((reply) => (
                  <CommentItem
                    key={reply.id}
                    comment={reply}
                  />
                ))}
              </div>
            )}
          </div>
        ))}
      </CardContent>
      <CardFooter>
        <CommentEditor />
      </CardFooter>
    </Card>
  );
}
