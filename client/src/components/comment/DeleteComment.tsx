import { useDeleteCommentMutation } from '@/services/comment';
import type { IComment } from '@/type/comment';
import type { FC } from 'react';
import { Button } from '../ui/button';
import { Trash } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

interface props {
  comment: IComment;
}
const DeleteComment: FC<props> = ({ comment }) => {
  const deleteCommentMutation = useDeleteCommentMutation();
  const auth = useAuth();
  if (
    (!auth.isAuthenticated() || auth.user('id') !== comment.user_id) &&
    !auth.isAdmin()
  )
    return null;

  return (
    <Button
      variant='ghost'
      size='icon'
      onClick={() => {
        deleteCommentMutation.mutate(comment.id);
      }}
    >
      <Trash />
    </Button>
  );
};
export default DeleteComment;
