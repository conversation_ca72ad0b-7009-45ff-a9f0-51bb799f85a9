import type { IComment } from '@/type/comment';
import type { FC } from 'react';
import UserIcon from '../user/UserIcon';
import { Next, Time } from '@icon-park/react';
import dayjs from 'dayjs';
import { useCommentContext } from '@/context/CommentContext';
import { Link } from '@tanstack/react-router';
import DeleteComment from './DeleteComment';

interface Props {
  comment: IComment;
}

const CommentAvatar: FC<Props> = ({ comment }) => {
  const { setReplyId, replyId } = useCommentContext();
  return (
    <main className='flex items-center justify-between pr-3 border-b'>
      <div className='flex items-center gap-2 p-3'>
        <UserIcon user={comment.user} />
        <div className='flex flex-col'>
          {comment.user.name}
          <div className='flex text-muted-foreground text-xs gap-2'>
            {comment.reply_user && (
              <Link to='/'>@{comment.reply_user.name}</Link>
            )}
            <div className='flex items-center'>
              <Time
                theme='outline'
                size='12'
              />
              {dayjs(comment.created_at).fromNow()}
            </div>
            <span
              className='flex items-center hover:text-primary cursor-pointer'
              onClick={() => {
                if (replyId === comment.id) {
                  setReplyId(0);
                } else {
                  setReplyId(comment.id);
                }
              }}
            >
              <Next
                theme='outline'
                size='12'
              />
              reply
            </span>
          </div>
        </div>
      </div>
      <DeleteComment comment={comment} />
    </main>
  );
};
export default CommentAvatar;
