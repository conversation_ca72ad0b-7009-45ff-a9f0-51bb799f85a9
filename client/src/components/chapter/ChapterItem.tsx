import type { IChapter } from '@/type/chapter';
import type { FC } from 'react';
import { Badge } from '../ui/badge';
import { Link } from '@tanstack/react-router';

interface Props {
  chapter: IChapter;
}

const ChapterItem: FC<Props> = ({ chapter }) => {
  return (
    <main className='bg-background border rounded-lg p-4 shadow-md flex flex-col'>
      <Link
        to={`/chapter/$id`}
        params={{ id: chapter.id }}
        className='cursor-pointer overflow-hidden flex rounded-md'
      >
        <img
          className='duration-300 hover:scale-125'
          src={chapter.preview}
          alt={chapter.title}
        />
      </Link>
      <Link
        to={`/chapter/$id`}
        params={{ id: chapter.id }}
        className='pt-3 flex truncate'
      >
        {chapter.title}
      </Link>
      <div className='font-light text-muted-foreground text-xs line-clamp-2 flex-1'>
        <p>{chapter.description}</p>
      </div>
      <div className='pt-3 flex items-center justify-between gap-2 border-t mt-3'>
        <div className='flex items-center gap-2'>
          <Badge variant='outline'>{chapter.video_num}</Badge>
          <span className='text-muted-foreground text-xs'>
            {chapter.video_num > 1 ? 'Videos' : 'Video'}
          </span>
        </div>
      </div>
    </main>
  );
};
export default ChapterItem;
