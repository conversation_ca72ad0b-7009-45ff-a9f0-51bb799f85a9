import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { IChapter } from '@/type/chapter';
import { ListTop } from '@icon-park/react';
import { Link } from '@tanstack/react-router';
import type { FC } from 'react';

const ChapterDownList: FC<{ chapters: IChapter[] }> = ({ chapters }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='outline'
          size='sm'
        >
          <ListTop
            theme='outline'
            size='16'
          />
          Chapters
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {chapters.map((c) => (
          <DropdownMenuItem key={c.id}>
            <Link
              to='/chapter/$id'
              params={{ id: c.id }}
            >
              {c.title}
            </Link>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
export default ChapterDownList;
