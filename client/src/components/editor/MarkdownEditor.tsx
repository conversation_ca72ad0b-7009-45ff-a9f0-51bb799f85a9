import {
  useEffect,
  useImperativeHandle,
  useRef,
  type FC,
  type Ref,
} from 'react';
import Cherry from 'cherry-markdown';
import 'cherry-markdown/dist/cherry-markdown.css';
import { cn } from '@/lib/utils';
import { useUploadImageMutation } from '@/services/upload';
export interface MarkdownEditorRef {
  clear: () => void;
}
interface Props {
  className?: string;
  value?: string;
  onChange: (value: string) => void;
  ref: Ref<MarkdownEditorRef>;
}

const MarkdownEditor: FC<Props> = ({
  ref,
  className,
  value = '',
  onChange,
}) => {
  const editorRef = useRef<Cherry | null>(null);
  const uploadImageMutation = useUploadImageMutation();

  useImperativeHandle(ref, () => ({
    clear: () => {
      editorRef.current?.setValue('');
    },
  }));

  useEffect(() => {
    editorRef.current = new Cherry({
      id: 'markdown-container',
      value,
      event: {
        afterChange: onChange,
      },
      fileUpload(file, callback) {
        uploadImageMutation.mutate(file, {
          onSuccess: ({ url }) => {
            callback(url);
          },
        });
      },
    });

    return () => {
      editorRef.current?.destroy();
    };
  }, [value, onChange]);

  return (
    <main className={cn(className)}>
      <div id='markdown-container'></div>
    </main>
  );
};
export default MarkdownEditor;
