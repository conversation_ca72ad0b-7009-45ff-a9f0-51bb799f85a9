import FieldValidate from '../common/FieldValidate';
import { Label } from '../ui/label';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { useFieldContext } from './FormContext';

interface RadioFieldProps {
  label: string;
  options: { label: string; value: string; id: string }[];
}

const RadioField = ({ label, options }: RadioFieldProps) => {
  const field = useFieldContext<string>();

  return (
    <div className='flex flex-col mb-2'>
      <Label
        className='mb-2'
        htmlFor='sex'
      >
        {label}
      </Label>
      <RadioGroup
        className='flex'
        value={String(field.state.value)}
        onValueChange={(value) => field.handleChange(value)}
      >
        {options.map((option) => (
          <div
            key={option.id}
            className='flex items-center space-x-2'
          >
            <RadioGroupItem
              value={option.value}
              id={option.id}
            />
            <Label htmlFor={option.id}>{option.label}</Label>
          </div>
        ))}
      </RadioGroup>
      <FieldValidate
        name={field.name}
        errors={field.state.meta.errors}
      />
    </div>
  );
};
export default RadioField;
