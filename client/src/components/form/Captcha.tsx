import React, { useImperativeHandle, useRef, type FC, type Ref } from 'react';
import { Input } from '../ui/input';
import FieldValidate from '../common/FieldValidate';
import { useFieldContext } from './FormContext';
export type ICaptchaRef = { refresh: () => void };
interface Props {
  ref: Ref<ICaptchaRef>;
}

const Captcha: FC<Props> = ({ ref }) => {
  const imgRef = useRef<HTMLImageElement | null>(null);

  useImperativeHandle(ref, () => {
    return {
      refresh: () => {
        if (imgRef.current) {
          imgRef.current.src = '/captcha/math?' + Math.random();
        }
      },
    };
  });

  const field = useFieldContext<string>();

  return (
    <div className='flex flex-col mb-2'>
      <div className='grid grid-cols-[1fr_auto] gap-2 items-center'>
        <Input
          placeholder='Enter the captcha'
          onChange={(e) => field.handleChange(e.target.value)}
        />
        <img
          ref={imgRef}
          src='/captcha/math'
          className='rounded-sm cursor-pointer border'
          onClick={(e) => {
            e.currentTarget.src = '/captcha/math?' + Math.random();
          }}
        />
      </div>
      <FieldValidate
        errors={field.state.meta.errors}
        name={field.name}
      />
    </div>
  );
};

export default Captcha;
