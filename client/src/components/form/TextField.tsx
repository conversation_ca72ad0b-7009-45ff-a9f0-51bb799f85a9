import type { FC, InputHTMLAttributes } from 'react';
import FieldValidate from '../common/FieldValidate';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useFieldContext } from './FormContext';

interface Props extends InputHTMLAttributes<HTMLInputElement> {
  fieldLabel?: string;
  type?: string;
}
const TextField: FC<Props> = ({ fieldLabel, ...props }) => {
  const field = useFieldContext<string>();

  return (
    <div className='flex flex-col mb-2'>
      {fieldLabel && (
        <Label
          className='mb-2'
          htmlFor={field.name}
        >
          {fieldLabel}
        </Label>
      )}
      <Input
        id={field.name}
        value={(field.state.value as string) || ''}
        onChange={(e) => field.handleChange(e.target.value)}
        {...props}
      />
      <FieldValidate
        name={field.name}
        errors={field.state.meta.errors}
      />
    </div>
  );
};
export default TextField;
