import { createFormHook, createFormHookContexts } from '@tanstack/react-form';
import TextField from './TextField';
import RadioField from './RadioField';
import SendCode from './SendCode';
import Captcha from './Captcha';

export const { fieldContext, formContext, useFieldContext } =
  createFormHookContexts();

const { useAppForm } = createFormHook({
  fieldContext,
  formContext,
  fieldComponents: { TextField, RadioField, SendCode, Captcha },
  formComponents: {},
});

export default useAppForm;
