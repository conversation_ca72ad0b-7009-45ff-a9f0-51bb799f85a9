import {
  useEffect,
  useRef,
  useState,
  type FC,
  type InputHTMLAttributes,
} from 'react';
import { toast } from 'sonner';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { useAxios } from '@/hooks/useAxios';
import { useMutation } from '@tanstack/react-query';
import FieldValidate from '../common/FieldValidate';
import { Label } from '../ui/label';
import { useFieldContext } from './FormContext';
import dayjs from 'dayjs';

interface Props extends InputHTMLAttributes<HTMLInputElement> {
  field: 'email' | 'mobile';
  action: string;
  showLabel?: boolean;
}
const SendCode: FC<Props> = ({ field, action, showLabel = true, ...props }) => {
  // persist end time for calculating countdown if page refreshed
  const sendEndTime = localStorage.getItem('sendCodeEndTime');
  const [endTime, setEndTime] = useState(
    sendEndTime ? dayjs(sendEndTime) : dayjs()
  );
  const [countdown, setCountdown] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const [value, setValue] = useState('');
  const { axiosInstance } = useAxios();
  const fieldInstance = useFieldContext<string>();
  const createTimer = () => {
    timerRef.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  const sendCodeMutation = useMutation({
    mutationFn: async () => {
      await axiosInstance.post(action, {
        [field]: value,
      });
    },
    onSuccess: () => {
      const newEndTime = dayjs().add(60, 'second');
      // persist end time
      localStorage.setItem('sendCodeEndTime', newEndTime.toISOString());
      setEndTime(newEndTime);
      setCountdown(60);
      createTimer();
      toast('Code sent');
    },
  });

  useEffect(() => {
    // if endTime - currentTime > 0, set countdown
    const diff = endTime.diff(dayjs(), 'second');
    if (diff > 0) {
      setCountdown(diff);
      createTimer();
    }

    return () => {
      clearInterval(timerRef.current);
    };
  }, []);

  return (
    <div className='flex flex-col mb-2'>
      {showLabel && (
        <Label
          className='mb-2'
          htmlFor={field}
        >
          {field === 'email' ? 'Email' : 'Mobile'}
        </Label>
      )}
      <div className='grid grid-cols-[1fr_auto] gap-2 items-start'>
        <Input
          id={fieldInstance.name}
          value={value}
          onChange={(e) => {
            setValue(e.target.value);
            fieldInstance.handleChange(e.target.value);
          }}
          {...props}
        />
        <Button
          variant='outline'
          type='button'
          disabled={countdown > 0 || sendCodeMutation.isPending}
          onClick={() => {
            if (!value)
              return toast(
                'Please enter your ' +
                  (field === 'email' ? 'email' : 'mobile number')
              );
            sendCodeMutation.mutate();
          }}
        >
          {countdown === 0 ? 'Send Code' : `Resend in ${countdown}s`}
        </Button>
      </div>
      <FieldValidate
        name={fieldInstance.name}
        errors={fieldInstance.state.meta.errors}
      />
    </div>
  );
};
export default SendCode;
