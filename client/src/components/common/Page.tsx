import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
} from '@/components/ui/pagination';
import { cn } from '@/lib/utils';
import type { IMeta } from '@/type/paginate';
import type { FC } from 'react';

interface Props {
  meta: IMeta;
  onChange?: (page: number) => void;
  singlePageShow?: boolean;
}
const Page: FC<Props> = ({ meta, onChange, singlePageShow = true }) => {
  if (meta.last_page === 1 && !singlePageShow) return null;

  return (
    <Pagination>
      <PaginationContent>
        {meta.links.map((link) => (
          <PaginationItem key={link.label}>
            <PaginationLink
              to={link.url ?? '#'}
              size={'default'}
              className={cn('border', {
                'bg-primary hover:bg-primary text-white hover:text-white! cursor-default':
                  link.active,
                'bg-muted opacity-50 text-muted-foreground hover:text-muted-foreground! cursor-not-allowed':
                  link.url === null,
              })}
              onClick={(e) => {
                if (!link.url || link.active || onChange) e.preventDefault();
                // TODO: not finished on change event
                if (onChange) onChange(Number(link.label));
              }}
              dangerouslySetInnerHTML={{ __html: link.label }}
            />
          </PaginationItem>
        ))}
      </PaginationContent>
    </Pagination>
  );
};
export default Page;
