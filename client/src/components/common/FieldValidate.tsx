import { cn } from '@/lib/utils';
import { useValidateStore } from '@/store/useValidateStore';
import type { ValidationError } from '@tanstack/react-form';

const FieldValidate = ({
  errors,
  name,
  className,
}: {
  errors: ValidationError[] | [];
  name: string;
  className?: string;
}) => {
  // handle local form errors or custom errors, plus request errors from global
  const errorStoreData = useValidateStore((state) => state.errors);

  if (errors.length === 0 && !errorStoreData[name])
    return <div className='my-1'></div>;
  return (
    <div
      className={cn(
        'border bg-muted text-xs text-accent-foreground rounded-sm flex items-center h-6 px-2 my-1',
        className
      )}
    >
      {errors
        .map((err) => {
          // FIXME: using as to avoid type error
          return (err as { message: string }).message;
        })
        .join(', ') || errorStoreData[name].join(', ')}
    </div>
  );
};
export default FieldValidate;
