import { create } from 'zustand';

interface ValidateState {
  errors: Record<string, string[]>;
  setErrors: (errors: Record<string, string[]>) => void;
  clearErrors: () => void;
}

export const useValidateStore = create<ValidateState>()((set) => ({
  errors: {},
  setErrors: (errors) =>
    set((state) => ({
      errors: { ...state.errors, ...errors },
    })),
  clearErrors: () => set({ errors: {} }),
}));
