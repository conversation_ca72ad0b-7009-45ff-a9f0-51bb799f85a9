import {
  createContext,
  useContext,
  useState,
  type Dispatch,
  type ReactNode,
  type SetStateAction,
} from 'react';

interface ContextProps {
  replyId: number;
  setReplyId: Dispatch<SetStateAction<number>>;
  modelName: string;
  setModelName: Dispatch<SetStateAction<string>>;
  modelId: number;
  setModelId: Dispatch<SetStateAction<number>>;
}
const CommentContext = createContext<ContextProps | undefined>(undefined);

export const CommentProvider = ({ children }: { children: ReactNode }) => {
  const [replyId, setReplyId] = useState(0);
  const [modelName, setModelName] = useState('');
  const [modelId, setModelId] = useState(0);

  return (
    <CommentContext.Provider
      value={{
        replyId,
        setReplyId,
        modelName,
        setModelName,
        modelId,
        setModelId,
      }}
    >
      {children}
    </CommentContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useCommentContext = () => {
  const context = useContext(CommentContext);

  if (context === undefined) {
    throw new Error('useCommentContext must be used within a CommentProvider');
  }

  return context;
};
