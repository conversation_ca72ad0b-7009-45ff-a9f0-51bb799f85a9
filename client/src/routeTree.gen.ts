/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './pages/__root'
import { Route as AboutImport } from './pages/about'
import { Route as SpaceRouteImport } from './pages/space/route'
import { Route as MemberRouteImport } from './pages/member/route'
import { Route as AuthRouteImport } from './pages/auth/route'
import { Route as FrontRouteImport } from './pages/_front/route'
import { Route as FrontIndexImport } from './pages/_front/index'
import { Route as SpaceIdImport } from './pages/space/$id'
import { Route as MemberInfoImport } from './pages/member/info'
import { Route as MemberLinkImport } from './pages/member/Link'
import { Route as AuthRegisterImport } from './pages/auth/register'
import { Route as AuthLoginImport } from './pages/auth/login'
import { Route as AuthForgetImport } from './pages/auth/forget'
import { Route as FrontVideoIndexImport } from './pages/_front/video/index'
import { Route as FrontTopicIndexImport } from './pages/_front/topic/index'
import { Route as FrontSignIndexImport } from './pages/_front/sign/index'
import { Route as FrontLessonIndexImport } from './pages/_front/lesson/index'
import { Route as FrontChapterIndexImport } from './pages/_front/chapter/index'
import { Route as FrontVideoIdImport } from './pages/_front/video/$id'
import { Route as FrontTopicCreateImport } from './pages/_front/topic/create'
import { Route as FrontTopicIdImport } from './pages/_front/topic/$id'
import { Route as FrontChapterIdImport } from './pages/_front/chapter/$id'
import { Route as FrontTopicEditIdImport } from './pages/_front/topic/edit.$id'
import { Route as FrontLessonShowIdImport } from './pages/_front/lesson/show.$id'

// Create/Update Routes

const AboutRoute = AboutImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRoute,
} as any)

const SpaceRouteRoute = SpaceRouteImport.update({
  id: '/space',
  path: '/space',
  getParentRoute: () => rootRoute,
} as any)

const MemberRouteRoute = MemberRouteImport.update({
  id: '/member',
  path: '/member',
  getParentRoute: () => rootRoute,
} as any)

const AuthRouteRoute = AuthRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRoute,
} as any)

const FrontRouteRoute = FrontRouteImport.update({
  id: '/_front',
  getParentRoute: () => rootRoute,
} as any)

const FrontIndexRoute = FrontIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => FrontRouteRoute,
} as any)

const SpaceIdRoute = SpaceIdImport.update({
  id: '/$id',
  path: '/$id',
  getParentRoute: () => SpaceRouteRoute,
} as any)

const MemberInfoRoute = MemberInfoImport.update({
  id: '/info',
  path: '/info',
  getParentRoute: () => MemberRouteRoute,
} as any)

const MemberLinkRoute = MemberLinkImport.update({
  id: '/Link',
  path: '/Link',
  getParentRoute: () => MemberRouteRoute,
} as any)

const AuthRegisterRoute = AuthRegisterImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => AuthRouteRoute,
} as any)

const AuthLoginRoute = AuthLoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRouteRoute,
} as any)

const AuthForgetRoute = AuthForgetImport.update({
  id: '/forget',
  path: '/forget',
  getParentRoute: () => AuthRouteRoute,
} as any)

const FrontVideoIndexRoute = FrontVideoIndexImport.update({
  id: '/video/',
  path: '/video/',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontTopicIndexRoute = FrontTopicIndexImport.update({
  id: '/topic/',
  path: '/topic/',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontSignIndexRoute = FrontSignIndexImport.update({
  id: '/sign/',
  path: '/sign/',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontLessonIndexRoute = FrontLessonIndexImport.update({
  id: '/lesson/',
  path: '/lesson/',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontChapterIndexRoute = FrontChapterIndexImport.update({
  id: '/chapter/',
  path: '/chapter/',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontVideoIdRoute = FrontVideoIdImport.update({
  id: '/video/$id',
  path: '/video/$id',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontTopicCreateRoute = FrontTopicCreateImport.update({
  id: '/topic/create',
  path: '/topic/create',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontTopicIdRoute = FrontTopicIdImport.update({
  id: '/topic/$id',
  path: '/topic/$id',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontChapterIdRoute = FrontChapterIdImport.update({
  id: '/chapter/$id',
  path: '/chapter/$id',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontTopicEditIdRoute = FrontTopicEditIdImport.update({
  id: '/topic/edit/$id',
  path: '/topic/edit/$id',
  getParentRoute: () => FrontRouteRoute,
} as any)

const FrontLessonShowIdRoute = FrontLessonShowIdImport.update({
  id: '/lesson/show/$id',
  path: '/lesson/show/$id',
  getParentRoute: () => FrontRouteRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_front': {
      id: '/_front'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof FrontRouteImport
      parentRoute: typeof rootRoute
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRoute
    }
    '/member': {
      id: '/member'
      path: '/member'
      fullPath: '/member'
      preLoaderRoute: typeof MemberRouteImport
      parentRoute: typeof rootRoute
    }
    '/space': {
      id: '/space'
      path: '/space'
      fullPath: '/space'
      preLoaderRoute: typeof SpaceRouteImport
      parentRoute: typeof rootRoute
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutImport
      parentRoute: typeof rootRoute
    }
    '/auth/forget': {
      id: '/auth/forget'
      path: '/forget'
      fullPath: '/auth/forget'
      preLoaderRoute: typeof AuthForgetImport
      parentRoute: typeof AuthRouteImport
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginImport
      parentRoute: typeof AuthRouteImport
    }
    '/auth/register': {
      id: '/auth/register'
      path: '/register'
      fullPath: '/auth/register'
      preLoaderRoute: typeof AuthRegisterImport
      parentRoute: typeof AuthRouteImport
    }
    '/member/Link': {
      id: '/member/Link'
      path: '/Link'
      fullPath: '/member/Link'
      preLoaderRoute: typeof MemberLinkImport
      parentRoute: typeof MemberRouteImport
    }
    '/member/info': {
      id: '/member/info'
      path: '/info'
      fullPath: '/member/info'
      preLoaderRoute: typeof MemberInfoImport
      parentRoute: typeof MemberRouteImport
    }
    '/space/$id': {
      id: '/space/$id'
      path: '/$id'
      fullPath: '/space/$id'
      preLoaderRoute: typeof SpaceIdImport
      parentRoute: typeof SpaceRouteImport
    }
    '/_front/': {
      id: '/_front/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof FrontIndexImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/chapter/$id': {
      id: '/_front/chapter/$id'
      path: '/chapter/$id'
      fullPath: '/chapter/$id'
      preLoaderRoute: typeof FrontChapterIdImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/topic/$id': {
      id: '/_front/topic/$id'
      path: '/topic/$id'
      fullPath: '/topic/$id'
      preLoaderRoute: typeof FrontTopicIdImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/topic/create': {
      id: '/_front/topic/create'
      path: '/topic/create'
      fullPath: '/topic/create'
      preLoaderRoute: typeof FrontTopicCreateImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/video/$id': {
      id: '/_front/video/$id'
      path: '/video/$id'
      fullPath: '/video/$id'
      preLoaderRoute: typeof FrontVideoIdImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/chapter/': {
      id: '/_front/chapter/'
      path: '/chapter'
      fullPath: '/chapter'
      preLoaderRoute: typeof FrontChapterIndexImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/lesson/': {
      id: '/_front/lesson/'
      path: '/lesson'
      fullPath: '/lesson'
      preLoaderRoute: typeof FrontLessonIndexImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/sign/': {
      id: '/_front/sign/'
      path: '/sign'
      fullPath: '/sign'
      preLoaderRoute: typeof FrontSignIndexImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/topic/': {
      id: '/_front/topic/'
      path: '/topic'
      fullPath: '/topic'
      preLoaderRoute: typeof FrontTopicIndexImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/video/': {
      id: '/_front/video/'
      path: '/video'
      fullPath: '/video'
      preLoaderRoute: typeof FrontVideoIndexImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/lesson/show/$id': {
      id: '/_front/lesson/show/$id'
      path: '/lesson/show/$id'
      fullPath: '/lesson/show/$id'
      preLoaderRoute: typeof FrontLessonShowIdImport
      parentRoute: typeof FrontRouteImport
    }
    '/_front/topic/edit/$id': {
      id: '/_front/topic/edit/$id'
      path: '/topic/edit/$id'
      fullPath: '/topic/edit/$id'
      preLoaderRoute: typeof FrontTopicEditIdImport
      parentRoute: typeof FrontRouteImport
    }
  }
}

// Create and export the route tree

interface FrontRouteRouteChildren {
  FrontIndexRoute: typeof FrontIndexRoute
  FrontChapterIdRoute: typeof FrontChapterIdRoute
  FrontTopicIdRoute: typeof FrontTopicIdRoute
  FrontTopicCreateRoute: typeof FrontTopicCreateRoute
  FrontVideoIdRoute: typeof FrontVideoIdRoute
  FrontChapterIndexRoute: typeof FrontChapterIndexRoute
  FrontLessonIndexRoute: typeof FrontLessonIndexRoute
  FrontSignIndexRoute: typeof FrontSignIndexRoute
  FrontTopicIndexRoute: typeof FrontTopicIndexRoute
  FrontVideoIndexRoute: typeof FrontVideoIndexRoute
  FrontLessonShowIdRoute: typeof FrontLessonShowIdRoute
  FrontTopicEditIdRoute: typeof FrontTopicEditIdRoute
}

const FrontRouteRouteChildren: FrontRouteRouteChildren = {
  FrontIndexRoute: FrontIndexRoute,
  FrontChapterIdRoute: FrontChapterIdRoute,
  FrontTopicIdRoute: FrontTopicIdRoute,
  FrontTopicCreateRoute: FrontTopicCreateRoute,
  FrontVideoIdRoute: FrontVideoIdRoute,
  FrontChapterIndexRoute: FrontChapterIndexRoute,
  FrontLessonIndexRoute: FrontLessonIndexRoute,
  FrontSignIndexRoute: FrontSignIndexRoute,
  FrontTopicIndexRoute: FrontTopicIndexRoute,
  FrontVideoIndexRoute: FrontVideoIndexRoute,
  FrontLessonShowIdRoute: FrontLessonShowIdRoute,
  FrontTopicEditIdRoute: FrontTopicEditIdRoute,
}

const FrontRouteRouteWithChildren = FrontRouteRoute._addFileChildren(
  FrontRouteRouteChildren,
)

interface AuthRouteRouteChildren {
  AuthForgetRoute: typeof AuthForgetRoute
  AuthLoginRoute: typeof AuthLoginRoute
  AuthRegisterRoute: typeof AuthRegisterRoute
}

const AuthRouteRouteChildren: AuthRouteRouteChildren = {
  AuthForgetRoute: AuthForgetRoute,
  AuthLoginRoute: AuthLoginRoute,
  AuthRegisterRoute: AuthRegisterRoute,
}

const AuthRouteRouteWithChildren = AuthRouteRoute._addFileChildren(
  AuthRouteRouteChildren,
)

interface MemberRouteRouteChildren {
  MemberLinkRoute: typeof MemberLinkRoute
  MemberInfoRoute: typeof MemberInfoRoute
}

const MemberRouteRouteChildren: MemberRouteRouteChildren = {
  MemberLinkRoute: MemberLinkRoute,
  MemberInfoRoute: MemberInfoRoute,
}

const MemberRouteRouteWithChildren = MemberRouteRoute._addFileChildren(
  MemberRouteRouteChildren,
)

interface SpaceRouteRouteChildren {
  SpaceIdRoute: typeof SpaceIdRoute
}

const SpaceRouteRouteChildren: SpaceRouteRouteChildren = {
  SpaceIdRoute: SpaceIdRoute,
}

const SpaceRouteRouteWithChildren = SpaceRouteRoute._addFileChildren(
  SpaceRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '': typeof FrontRouteRouteWithChildren
  '/auth': typeof AuthRouteRouteWithChildren
  '/member': typeof MemberRouteRouteWithChildren
  '/space': typeof SpaceRouteRouteWithChildren
  '/about': typeof AboutRoute
  '/auth/forget': typeof AuthForgetRoute
  '/auth/login': typeof AuthLoginRoute
  '/auth/register': typeof AuthRegisterRoute
  '/member/Link': typeof MemberLinkRoute
  '/member/info': typeof MemberInfoRoute
  '/space/$id': typeof SpaceIdRoute
  '/': typeof FrontIndexRoute
  '/chapter/$id': typeof FrontChapterIdRoute
  '/topic/$id': typeof FrontTopicIdRoute
  '/topic/create': typeof FrontTopicCreateRoute
  '/video/$id': typeof FrontVideoIdRoute
  '/chapter': typeof FrontChapterIndexRoute
  '/lesson': typeof FrontLessonIndexRoute
  '/sign': typeof FrontSignIndexRoute
  '/topic': typeof FrontTopicIndexRoute
  '/video': typeof FrontVideoIndexRoute
  '/lesson/show/$id': typeof FrontLessonShowIdRoute
  '/topic/edit/$id': typeof FrontTopicEditIdRoute
}

export interface FileRoutesByTo {
  '/auth': typeof AuthRouteRouteWithChildren
  '/member': typeof MemberRouteRouteWithChildren
  '/space': typeof SpaceRouteRouteWithChildren
  '/about': typeof AboutRoute
  '/auth/forget': typeof AuthForgetRoute
  '/auth/login': typeof AuthLoginRoute
  '/auth/register': typeof AuthRegisterRoute
  '/member/Link': typeof MemberLinkRoute
  '/member/info': typeof MemberInfoRoute
  '/space/$id': typeof SpaceIdRoute
  '/': typeof FrontIndexRoute
  '/chapter/$id': typeof FrontChapterIdRoute
  '/topic/$id': typeof FrontTopicIdRoute
  '/topic/create': typeof FrontTopicCreateRoute
  '/video/$id': typeof FrontVideoIdRoute
  '/chapter': typeof FrontChapterIndexRoute
  '/lesson': typeof FrontLessonIndexRoute
  '/sign': typeof FrontSignIndexRoute
  '/topic': typeof FrontTopicIndexRoute
  '/video': typeof FrontVideoIndexRoute
  '/lesson/show/$id': typeof FrontLessonShowIdRoute
  '/topic/edit/$id': typeof FrontTopicEditIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_front': typeof FrontRouteRouteWithChildren
  '/auth': typeof AuthRouteRouteWithChildren
  '/member': typeof MemberRouteRouteWithChildren
  '/space': typeof SpaceRouteRouteWithChildren
  '/about': typeof AboutRoute
  '/auth/forget': typeof AuthForgetRoute
  '/auth/login': typeof AuthLoginRoute
  '/auth/register': typeof AuthRegisterRoute
  '/member/Link': typeof MemberLinkRoute
  '/member/info': typeof MemberInfoRoute
  '/space/$id': typeof SpaceIdRoute
  '/_front/': typeof FrontIndexRoute
  '/_front/chapter/$id': typeof FrontChapterIdRoute
  '/_front/topic/$id': typeof FrontTopicIdRoute
  '/_front/topic/create': typeof FrontTopicCreateRoute
  '/_front/video/$id': typeof FrontVideoIdRoute
  '/_front/chapter/': typeof FrontChapterIndexRoute
  '/_front/lesson/': typeof FrontLessonIndexRoute
  '/_front/sign/': typeof FrontSignIndexRoute
  '/_front/topic/': typeof FrontTopicIndexRoute
  '/_front/video/': typeof FrontVideoIndexRoute
  '/_front/lesson/show/$id': typeof FrontLessonShowIdRoute
  '/_front/topic/edit/$id': typeof FrontTopicEditIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/auth'
    | '/member'
    | '/space'
    | '/about'
    | '/auth/forget'
    | '/auth/login'
    | '/auth/register'
    | '/member/Link'
    | '/member/info'
    | '/space/$id'
    | '/'
    | '/chapter/$id'
    | '/topic/$id'
    | '/topic/create'
    | '/video/$id'
    | '/chapter'
    | '/lesson'
    | '/sign'
    | '/topic'
    | '/video'
    | '/lesson/show/$id'
    | '/topic/edit/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/auth'
    | '/member'
    | '/space'
    | '/about'
    | '/auth/forget'
    | '/auth/login'
    | '/auth/register'
    | '/member/Link'
    | '/member/info'
    | '/space/$id'
    | '/'
    | '/chapter/$id'
    | '/topic/$id'
    | '/topic/create'
    | '/video/$id'
    | '/chapter'
    | '/lesson'
    | '/sign'
    | '/topic'
    | '/video'
    | '/lesson/show/$id'
    | '/topic/edit/$id'
  id:
    | '__root__'
    | '/_front'
    | '/auth'
    | '/member'
    | '/space'
    | '/about'
    | '/auth/forget'
    | '/auth/login'
    | '/auth/register'
    | '/member/Link'
    | '/member/info'
    | '/space/$id'
    | '/_front/'
    | '/_front/chapter/$id'
    | '/_front/topic/$id'
    | '/_front/topic/create'
    | '/_front/video/$id'
    | '/_front/chapter/'
    | '/_front/lesson/'
    | '/_front/sign/'
    | '/_front/topic/'
    | '/_front/video/'
    | '/_front/lesson/show/$id'
    | '/_front/topic/edit/$id'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  FrontRouteRoute: typeof FrontRouteRouteWithChildren
  AuthRouteRoute: typeof AuthRouteRouteWithChildren
  MemberRouteRoute: typeof MemberRouteRouteWithChildren
  SpaceRouteRoute: typeof SpaceRouteRouteWithChildren
  AboutRoute: typeof AboutRoute
}

const rootRouteChildren: RootRouteChildren = {
  FrontRouteRoute: FrontRouteRouteWithChildren,
  AuthRouteRoute: AuthRouteRouteWithChildren,
  MemberRouteRoute: MemberRouteRouteWithChildren,
  SpaceRouteRoute: SpaceRouteRouteWithChildren,
  AboutRoute: AboutRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_front",
        "/auth",
        "/member",
        "/space",
        "/about"
      ]
    },
    "/_front": {
      "filePath": "_front/route.tsx",
      "children": [
        "/_front/",
        "/_front/chapter/$id",
        "/_front/topic/$id",
        "/_front/topic/create",
        "/_front/video/$id",
        "/_front/chapter/",
        "/_front/lesson/",
        "/_front/sign/",
        "/_front/topic/",
        "/_front/video/",
        "/_front/lesson/show/$id",
        "/_front/topic/edit/$id"
      ]
    },
    "/auth": {
      "filePath": "auth/route.tsx",
      "children": [
        "/auth/forget",
        "/auth/login",
        "/auth/register"
      ]
    },
    "/member": {
      "filePath": "member/route.tsx",
      "children": [
        "/member/Link",
        "/member/info"
      ]
    },
    "/space": {
      "filePath": "space/route.tsx",
      "children": [
        "/space/$id"
      ]
    },
    "/about": {
      "filePath": "about.tsx"
    },
    "/auth/forget": {
      "filePath": "auth/forget.tsx",
      "parent": "/auth"
    },
    "/auth/login": {
      "filePath": "auth/login.tsx",
      "parent": "/auth"
    },
    "/auth/register": {
      "filePath": "auth/register.tsx",
      "parent": "/auth"
    },
    "/member/Link": {
      "filePath": "member/Link.tsx",
      "parent": "/member"
    },
    "/member/info": {
      "filePath": "member/info.tsx",
      "parent": "/member"
    },
    "/space/$id": {
      "filePath": "space/$id.tsx",
      "parent": "/space"
    },
    "/_front/": {
      "filePath": "_front/index.tsx",
      "parent": "/_front"
    },
    "/_front/chapter/$id": {
      "filePath": "_front/chapter/$id.tsx",
      "parent": "/_front"
    },
    "/_front/topic/$id": {
      "filePath": "_front/topic/$id.tsx",
      "parent": "/_front"
    },
    "/_front/topic/create": {
      "filePath": "_front/topic/create.tsx",
      "parent": "/_front"
    },
    "/_front/video/$id": {
      "filePath": "_front/video/$id.tsx",
      "parent": "/_front"
    },
    "/_front/chapter/": {
      "filePath": "_front/chapter/index.tsx",
      "parent": "/_front"
    },
    "/_front/lesson/": {
      "filePath": "_front/lesson/index.tsx",
      "parent": "/_front"
    },
    "/_front/sign/": {
      "filePath": "_front/sign/index.tsx",
      "parent": "/_front"
    },
    "/_front/topic/": {
      "filePath": "_front/topic/index.tsx",
      "parent": "/_front"
    },
    "/_front/video/": {
      "filePath": "_front/video/index.tsx",
      "parent": "/_front"
    },
    "/_front/lesson/show/$id": {
      "filePath": "_front/lesson/show.$id.tsx",
      "parent": "/_front"
    },
    "/_front/topic/edit/$id": {
      "filePath": "_front/topic/edit.$id.tsx",
      "parent": "/_front"
    }
  }
}
ROUTE_MANIFEST_END */
