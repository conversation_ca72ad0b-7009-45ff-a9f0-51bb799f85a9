import Page from '@/components/common/Page';
import Error from '@/components/error/Error';
import Loading from '@/components/loading/Loading';
import TopicItem from '@/components/topic/TopicItem';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useGetTopicListQuery } from '@/services/topic';
import { createFileRoute, Link } from '@tanstack/react-router';

export const Route = createFileRoute('/_front/topic/')({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>): { page?: number } => {
    return { page: Number(search.page) || 1 };
  },
});

function RouteComponent() {
  const { page } = Route.useSearch();
  const {
    data: topicList,
    isPending,
    isError,
    error,
  } = useGetTopicListQuery({ page: page || 1 });

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <main className='w-7xl mx-auto'>
      <Card>
        <CardHeader>
          <CardTitle>Discussion Forum</CardTitle>
          <CardDescription className='flex items-center gap-2 justify-between'>
            Share your thoughts and ideas with the community
            <Link to={'/topic/create'}>
              <Button variant={'outline'}>New Topic</Button>
            </Link>
          </CardDescription>
        </CardHeader>
        <CardContent>
          {topicList.data.map((topic) => (
            <TopicItem
              key={topic.id}
              topic={topic}
            />
          ))}
        </CardContent>
        <CardFooter>
          <Page meta={topicList.meta} />
        </CardFooter>
      </Card>
    </main>
  );
}
