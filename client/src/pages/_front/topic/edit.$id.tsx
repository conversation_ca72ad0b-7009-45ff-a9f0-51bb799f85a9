import Error from '@/components/error/Error';
import { useGetTopicQuery } from '@/services/topic';
import { createFileRoute } from '@tanstack/react-router';
import { Form } from './-components/Form';
import Loading from '@/components/loading/Loading';

export const Route = createFileRoute('/_front/topic/edit/$id')({
  component: RouteComponent,
  params: {
    parse: ({ id }) => ({ id: Number(id) }),
  },
});

function RouteComponent() {
  const { id } = Route.useParams();
  const { data: topic, isPending, isError, error } = useGetTopicQuery(id);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return <Form defaultValues={topic} />;
}
