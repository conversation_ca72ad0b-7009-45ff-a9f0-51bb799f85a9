import CommentList from '@/components/comment/CommentList';
import Error from '@/components/error/Error';
import Loading from '@/components/loading/Loading';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import UserIcon from '@/components/user/UserIcon';
import { useAuth } from '@/hooks/useAuth';
import { useDeleteTopicMutation, useGetTopicQuery } from '@/services/topic';
import { createFileRoute, Link } from '@tanstack/react-router';
import dayjs from 'dayjs';

export const Route = createFileRoute('/_front/topic/$id')({
  component: RouteComponent,
  params: {
    parse: ({ id }) => {
      return {
        id: Number(id),
      };
    },
  },
});

function RouteComponent() {
  const { id } = Route.useParams();
  const auth = useAuth();
  const { data: topic, isPending, isError, error } = useGetTopicQuery(id);

  const deleteTopic = useDeleteTopicMutation();

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <main className='xl:w-7xl mx-auto'>
      <Card>
        <CardHeader>
          <CardTitle>{topic.title}</CardTitle>
          <CardDescription className='flex justify-between'>
            <div className='flex items-center gap-2 mt-3'>
              <UserIcon user={topic.user} />

              <div>
                <span>{topic.user.name}</span>
                <div>posted at {dayjs(topic.created_at).fromNow()}</div>
              </div>
            </div>
            {auth.isAuthenticated() && auth.user('id') === topic.user.id && (
              <div className='flex gap-2'>
                <Button
                  variant={'outline'}
                  size='sm'
                  onClick={() => {
                    deleteTopic.mutate(topic.id);
                  }}
                >
                  Delete
                </Button>
                <Link
                  to={'/topic/edit/$id'}
                  params={{ id: topic.id }}
                >
                  <Button
                    variant={'outline'}
                    size='sm'
                  >
                    Edit
                  </Button>
                </Link>
              </div>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <article
            className='prose prose-h1:text-xl max-w-full lg:prose-xl'
            dangerouslySetInnerHTML={{ __html: topic.html }}
          />
        </CardContent>
        <CardFooter>
          <p>Card Footer</p>
        </CardFooter>
      </Card>
      <CommentList
        modelName='Topic'
        modelId={topic.id}
      />
    </main>
  );
}
