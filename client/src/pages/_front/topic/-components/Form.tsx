import FieldValidate from '@/components/common/FieldValidate';
import MarkdownEditor, {
  type MarkdownEditorRef,
} from '@/components/editor/MarkdownEditor';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  useSubmitTopicMutation,
  useUpdateTopicMutation,
} from '@/services/topic';
import type { ITopic } from '@/type/topic';
import { useForm } from '@tanstack/react-form';
import { useNavigate } from '@tanstack/react-router';
import { useRef, type FC } from 'react';
import { toast } from 'sonner';

interface Props {
  defaultValues: Partial<ITopic>;
}

export const Form: FC<Props> = ({ defaultValues }) => {
  const submitTopic = useSubmitTopicMutation();
  const updateTopic = useUpdateTopicMutation();
  const navigate = useNavigate();
  const editorRef = useRef<MarkdownEditorRef>(null);

  const form = useForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      const action = defaultValues.id ? updateTopic : submitTopic;

      action.mutate(value, {
        onSuccess: (data) => {
          toast.success('Topic submitted successfully');
          editorRef.current?.clear();
          navigate({ to: '/topic/$id', params: { id: data.id } });
        },
        onError: (error) => {
          console.log(error);
          toast.error('Topic submission failed');
        },
      });
    },
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <Card className='xl:w-7xl mx-auto'>
        <CardHeader>
          <CardTitle>
            {defaultValues.id ? 'Edit' : 'Create'} Your Post
          </CardTitle>
          <CardDescription>
            Please describe your question or share your thoughts with the
            community
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form.Field
            name='title'
            children={(field) => (
              <div>
                <Input
                  value={field.state.value}
                  onChange={(e) => {
                    field.handleChange(e.target.value);
                  }}
                  placeholder='Title'
                />
                <FieldValidate
                  errors={field.state.meta.errors}
                  name='title'
                />
              </div>
            )}
          ></form.Field>
          <form.Field
            name='content'
            children={(field) => (
              <div>
                <div className=''>
                  <MarkdownEditor
                    ref={editorRef}
                    className='h-[350px]'
                    onChange={field.handleChange}
                    value={defaultValues.content}
                  />
                </div>
                <FieldValidate
                  errors={field.state.meta.errors}
                  name='content'
                />
              </div>
            )}
          ></form.Field>
        </CardContent>
        <CardFooter>
          <Button variant={'default'}>Submit</Button>
        </CardFooter>
      </Card>
    </form>
  );
};
