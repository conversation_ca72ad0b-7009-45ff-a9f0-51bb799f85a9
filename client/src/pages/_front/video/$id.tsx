import ChapterDownList from '@/components/chapter/ChapterDownList';
import CommentList from '@/components/comment/CommentList';
import VideoPlayer from '@/components/common/VideoPlayer';
import Error from '@/components/error/Error';
import DownloadLesson from '@/components/lesson/DownloadLesson';
import Loading from '@/components/loading/Loading';
import VideoItem from '@/components/video/VideoItem';
import { cn } from '@/lib/utils';
import { useGetVideoDetailQuery } from '@/services/video';
import { Book, Right } from '@icon-park/react';
import { createFileRoute, Link, redirect } from '@tanstack/react-router';
import { useEffect } from 'react';
import { toast } from 'sonner';

export const Route = createFileRoute('/_front/video/$id')({
  component: RouteComponent,
  beforeLoad: ({ context }) => {
    if (!context.auth.isAuthenticated()) {
      toast('Please login first');
      throw redirect({ to: '/auth/login' });
    }
  },
  params: {
    parse: ({ id }: Record<string, unknown>) => ({
      id: Number(id),
    }),
  },
});

function RouteComponent() {
  const { id } = Route.useParams();
  const { data: video, isPending, isError, error } = useGetVideoDetailQuery(id);

  useEffect(() => {
    if (video) {
      const videoElement = document.getElementById(`video-${id}`);
      videoElement?.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  }, [id, video]);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <main className='-mt-6'>
      <section className='bg-accent-foreground'>
        <div className='xl:w-7xl mx-auto grid grid-cols-[1fr_300px]'>
          <VideoPlayer url={video.path} />
          <aside className='bg-muted p-3 relative'>
            <h2 className='text-sm font-bold pb-2'>Video List</h2>
            <div className='absolute left-0 top-10 right-0 bottom-0 overflow-y-auto px-3 py-2'>
              {video.chapter.videos.map((v) => (
                <div
                  id={`video-${v.id}`}
                  className=''
                >
                  <VideoItem
                    key={v.id}
                    video={v}
                    showTime={false}
                    className={cn(
                      { 'text-primary': v.id === video.id },
                      'text-sm'
                    )}
                  />
                </div>
              ))}
            </div>
          </aside>
        </div>
      </section>
      <section className='xl:w7-xl mx-auto bg-white p-6 rounded-lg mt-3 flex justify-between items-center'>
        <div className=''>
          <h2 className=''>{video.title}</h2>
          <div className='flex items-center gap-1 text-muted-foreground font-light text-sm mt-2'>
            <Book
              theme='outline'
              size='15'
              fill='#333'
            />
            <Link
              to='/lesson/show/$id'
              params={{ id: video.lesson_id }}
            >
              {video.lesson.title}
            </Link>
            <Right
              theme='outline'
              size='15'
              fill='#333'
            />
            <Link
              to='/chapter/$id'
              params={{ id: video.chapter_id }}
            >
              {video.chapter.title}
            </Link>
          </div>
        </div>
        <div className='flex items-center gap-2'>
          <DownloadLesson
            size='sm'
            variant={'outline'}
          />
          <ChapterDownList chapters={video.lesson.chapters} />
        </div>
      </section>
      <CommentList
        modelName='Video'
        modelId={video.id}
      />
    </main>
  );
}
