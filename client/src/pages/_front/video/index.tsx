import Page from '@/components/common/Page';
import Error from '@/components/error/Error';
import Loading from '@/components/loading/Loading';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import VideoItem from '@/components/video/VideoItem';
import { useGetVideoListQuery } from '@/services/video';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute('/_front/video/')({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>) => {
    return {
      page: Number(search.page) || 1,
    } as { page: number };
  },
});

function RouteComponent() {
  const { page } = Route.useSearch();
  const {
    data: videos,
    isPending,
    isError,
    error,
  } = useGetVideoListQuery(page);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <Card className='xl:w-7xl mx-auto'>
      <CardHeader>
        <CardTitle>Newest Video</CardTitle>
        <CardDescription>
          Here are the newest videos, you can watch them.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {videos.data.map((video) => (
          <VideoItem
            key={video.id}
            video={video}
          />
        ))}
      </CardContent>
      <CardFooter>
        <Page meta={videos.meta} />
      </CardFooter>
    </Card>
  );
}
