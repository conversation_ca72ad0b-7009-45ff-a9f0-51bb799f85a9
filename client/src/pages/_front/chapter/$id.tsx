import ChapterItem from '@/components/chapter/ChapterItem';
import Error from '@/components/error/Error';
import DownloadLesson from '@/components/lesson/DownloadLesson';
import Loading from '@/components/loading/Loading';
import AliPay from '@/components/pay/AliPay';
import WechatPay from '@/components/pay/WechatPay';
import { Badge } from '@/components/ui/badge';
import VideoItem from '@/components/video/VideoItem';
import { useGetChapterQuery } from '@/services/chapter';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute('/_front/chapter/$id')({
  component: RouteComponent,
  params: {
    parse: ({ id }) => {
      return { id: Number(id) };
    },
  },
});

function RouteComponent() {
  const { id } = Route.useParams();
  const { data: chapter, isPending, isError, error } = useGetChapterQuery(id);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <main className='-mt-6'>
      <section className='bg-accent-foreground py-12'>
        <div className='2xl:w-7xl mx-auto'>
          <div className='text-muted'>
            <h1 className='text-2xl mb-3 flex items-center gap-2'>
              <Badge
                variant={'default'}
                className='bg-green-700 hover:bg-green-700'
              >
                Chapter
              </Badge>{' '}
              {chapter.title}
            </h1>
            <div className='font-light'>{chapter.description}</div>
          </div>
          <div className='mt-6 flex items-center gap-3'>
            <DownloadLesson />
            <WechatPay />
            <AliPay />
          </div>
        </div>
      </section>
      <section className='xl:w-7xl m-auto mt-6 gap-3 grid grid-cols-[1fr_300px]'>
        <div className='bg-white p-6 rounded-lg'>
          <h2 className='mb-3'>Video List</h2>
          {chapter.videos.map((video) => (
            <VideoItem
              key={video.id}
              video={video}
            />
          ))}
        </div>
        <div className='-mt-32'>
          <ChapterItem chapter={chapter} />
        </div>
      </section>
    </main>
  );
}
