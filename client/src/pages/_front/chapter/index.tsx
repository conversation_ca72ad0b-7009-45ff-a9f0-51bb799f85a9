import ChapterItem from '@/components/chapter/ChapterItem';
import Page from '@/components/common/Page';
import Error from '@/components/error/Error';
import Loading from '@/components/loading/Loading';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useGetChapterListQuery } from '@/services/chapter';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute('/_front/chapter/')({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>) => {
    return {
      page: Number(search.page) || 1,
    };
  },
});

function RouteComponent() {
  const { page } = Route.useSearch();
  const { data, isPending, isError, error } = useGetChapterListQuery(page);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <Card className='xl:w-7xl mx-auto'>
      <CardHeader>
        <CardTitle>Chapters</CardTitle>
        <CardDescription>Latest updated chapters list</CardDescription>
      </CardHeader>
      <CardContent className='grid grid-cols-4 gap-3'>
        {data.data.map((chapter) => (
          <ChapterItem
            key={chapter.id}
            chapter={chapter}
          />
        ))}
      </CardContent>
      <CardFooter>
        <Page meta={data.meta} />
      </CardFooter>
    </Card>
  );
}
