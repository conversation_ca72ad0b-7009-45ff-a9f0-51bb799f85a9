import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from '@tanstack/react-router';

const NoLogin = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>You need to login to sign your thoughts</CardTitle>
      </CardHeader>
      <CardContent>
        <Link to='/auth/login'>
          <Button variant={'default'}>Login</Button>
        </Link>
      </CardContent>
    </Card>
  );
};
export default NoLogin;
