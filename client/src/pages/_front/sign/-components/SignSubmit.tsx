import type { FC } from 'react';
import type { ISign } from '@/type/sign';
import SignForm from './SignForm';
import { useAuth } from '@/hooks/useAuth';
import IsSignMessage from './IsSignMessage';

interface Props {
  signList: ISign[];
}

const SignSubmit: FC<Props> = ({ signList }) => {
  const { user } = useAuth();
  const isSign = signList.find((sign) => sign.user_id === user('id'));

  return isSign ? <IsSignMessage sign={isSign} /> : <SignForm />;
};
export default SignSubmit;
