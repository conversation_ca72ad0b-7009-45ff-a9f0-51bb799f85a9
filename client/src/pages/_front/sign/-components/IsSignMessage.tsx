import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import type { ISign } from '@/type/sign';
import dayjs from 'dayjs';
import type { FC } from 'react';

interface Props {
  sign: ISign;
}

const IsSignMessage: FC<Props> = ({ sign }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>You have signed today</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='flex items-center'>
          <img
            src={`/assets/images/emoji/${sign.mood}.gif`}
            alt='sign'
          />
          {sign.content}
        </div>
        <div className='flex flex-col gap-2 text-sm mt-3'>
          <span>Signed at {dayjs(sign.created_at).format('HH:mm:ss')}</span>
          <span>Total Sign: {sign.sign_count?.total} times</span>
          <span>This year: {sign.sign_count?.year} times</span>
          <span>This month: {sign.sign_count?.month} times</span>
        </div>
      </CardContent>
    </Card>
  );
};
export default IsSignMessage;
