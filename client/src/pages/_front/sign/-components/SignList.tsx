import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import UserIcon from '@/components/user/UserIcon';
import dayjs from 'dayjs';
import type { ISign } from '@/type/sign';
import type { FC } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { useDeleteSignMutation } from '@/services/sign';

interface Props {
  signList: ISign[];
}

const SignList: FC<Props> = ({ signList }) => {
  const { isAuthenticated, user } = useAuth();
  const deleteSignMutation = useDeleteSignMutation();

  return (
    <main>
      <Card className='mt-4'>
        <CardHeader>
          <CardTitle>Today's Sign</CardTitle>
        </CardHeader>
        <CardContent>
          <Table className='bg-white border'>
            <TableHeader>
              <TableRow>
                <TableHead className='w-[100px]'>Name</TableHead>
                <TableHead>Total Sign</TableHead>
                <TableHead>This month</TableHead>
                <TableHead>This year</TableHead>
                <TableHead>Signed At</TableHead>
                <TableHead>Feeling</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {signList.map((sign) => (
                <TableRow>
                  <TableCell className='font-medium'>
                    <div className='flex items-center gap-2'>
                      <UserIcon user={sign.user} />
                    </div>
                  </TableCell>
                  <TableCell>{sign.sign_count?.total}</TableCell>
                  <TableCell>{sign.sign_count?.month}</TableCell>
                  <TableCell>{sign.sign_count?.year}</TableCell>
                  <TableCell>
                    {dayjs(sign.created_at).format('HH:mm:ss')}
                  </TableCell>
                  <TableCell className='flex items-center gap-2 text-sm justify-between'>
                    <img
                      src={`/assets/images/emoji/${sign.mood}.gif`}
                      alt={sign.mood}
                      className='w-10 h-10'
                    />
                    {sign.content}
                    {isAuthenticated() && user('id') === sign.user_id && (
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => {
                          deleteSignMutation.mutate(sign.id);
                        }}
                      >
                        Delete
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </main>
  );
};
export default SignList;
