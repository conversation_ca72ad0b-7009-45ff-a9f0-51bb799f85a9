import FieldValidate from '@/components/common/FieldValidate';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { usePostSignMutation } from '@/services/sign';
import { useForm } from '@tanstack/react-form';
const emojis = ['ch', 'fd', 'kx', 'ng', 'nu', 'shuai', 'wl', 'yl', 'ym'];

const SignForm = () => {
  const postSignMutation = usePostSignMutation();
  const form = useForm({
    defaultValues: {
      content: '',
      mood: '',
    },
    onSubmit: ({ value }) => {
      postSignMutation.mutate(value, {
        onSuccess: () => {
          form.reset();
        },
      });
    },
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <Card>
        <CardHeader>
          <CardTitle>Share your thoughts today</CardTitle>
        </CardHeader>
        <CardContent>
          <form.Field
            name='content'
            children={(field) => {
              return (
                <div>
                  <Input
                    placeholder='Share your thoughts today'
                    value={field.state.value}
                    onChange={(e) => {
                      field.handleChange(e.target.value);
                    }}
                  />
                  <FieldValidate
                    errors={field.state.meta.errors}
                    name='content'
                  />
                </div>
              );
            }}
          />
          <form.Field
            name='mood'
            children={(field) => {
              return (
                <div>
                  <div className='flex flex-wrap gap-2 items-center'>
                    {emojis.map((emoji) => (
                      <div
                        className={cn(
                          'group hover:bg-muted p-1 rounded-md cursor-pointer',
                          field.state.value === emoji
                            ? 'bg-muted'
                            : 'hover:bg-muted'
                        )}
                        onClick={() => {
                          field.handleChange(emoji);
                        }}
                      >
                        <img
                          src={`/assets/images/emoji/${emoji}.gif`}
                          alt={emoji}
                          width={32}
                          height={32}
                          className='group-hover:scale-120 hover transition-all duration-300'
                        />
                      </div>
                    ))}
                  </div>
                  <FieldValidate
                    errors={field.state.meta.errors}
                    name='mood'
                  />
                </div>
              );
            }}
          />
          <Button
            variant='default'
            className='mt-3'
          >
            Submit
          </Button>
        </CardContent>
      </Card>
    </form>
  );
};
export default SignForm;
