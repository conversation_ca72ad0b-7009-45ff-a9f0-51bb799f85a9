import Error from '@/components/error/Error';
import Loading from '@/components/loading/Loading';
import { useAuth } from '@/hooks/useAuth';
import { useGetSignListQuery } from '@/services/sign';
import { createFileRoute } from '@tanstack/react-router';
import NoLogin from './-components/NoLogin';
import SignList from './-components/SignList';
import SignSubmit from './-components/SignSubmit';
import WechatSign from './-components/WechatSign';

export const Route = createFileRoute('/_front/sign/')({
  component: RouteComponent,
});

function RouteComponent() {
  const { isAuthenticated } = useAuth();
  const { data: signList, isPending, isError, error } = useGetSignListQuery();

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <main className='xl:w-7xl mx-auto'>
      {isAuthenticated() ? (
        <SignSubmit signList={signList ?? []} />
      ) : (
        <NoLogin />
      )}
      <WechatSign />
      <SignList signList={signList ?? []} />
    </main>
  );
}
