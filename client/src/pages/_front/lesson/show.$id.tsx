import ChapterItem from '@/components/chapter/ChapterItem';
import Error from '@/components/error/Error';
import DownloadLesson from '@/components/lesson/DownloadLesson';
import Loading from '@/components/loading/Loading';
import AliPay from '@/components/pay/AliPay';
import WechatPay from '@/components/pay/WechatPay';
import { Badge } from '@/components/ui/badge';
import { useGetLessonDetailQuery } from '@/services/lesson';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute('/_front/lesson/show/$id')({
  component: RouteComponent,
  params: {
    parse: ({ id }) => {
      return {
        id: Number(id),
      };
    },
  },
});

function RouteComponent() {
  const { id } = Route.useParams();

  const {
    isPending,
    isError,
    error,
    data: lesson,
  } = useGetLessonDetailQuery(id);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <main className='-mt-6'>
      <section className='bg-accent-foreground '>
        <div className='2xl:w-7xl mx-auto py-12'>
          <div className='text-muted'>
            <h1 className='text-2xl mb-3 flex items-center gap-2'>
              <Badge variant={'default'}>{lesson.type}</Badge>
              {lesson.title}
            </h1>
            <div className='font-light'>{lesson.description}</div>
          </div>
          <div className='mt-6 flex items-center gap-3'>
            <DownloadLesson />
            <WechatPay />
            <AliPay />
          </div>
        </div>
      </section>
      <section className='bg-white p-6 rounded-lg xl:w-7xl m-auto mt-6 grid grid-cols-4 gap-3'>
        {lesson.chapters.map((chapter) => (
          <ChapterItem
            key={chapter.id}
            chapter={chapter}
          />
        ))}
      </section>
    </main>
  );
}
