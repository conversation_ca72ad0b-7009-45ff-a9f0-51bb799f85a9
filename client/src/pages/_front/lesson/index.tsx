import Page from '@/components/common/Page';
import Error from '@/components/error/Error';
import LessonItem from '@/components/lesson/LessonItem';
import Loading from '@/components/loading/Loading';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { lessonTitle } from '@/config/app';
import { useGetLessonListQuery } from '@/services/lesson';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute('/_front/lesson/')({
  // get type from url, and check if it is in the lessonTitle, if not, set it to the default 'system'
  // performing type set here to avoid the type error
  // params: {
  //   parse: ({ type }) => {
  //     return {
  //       type: Object.keys(lessonTitle).includes(type) ? type : 'system',
  //     } as IRouteParams;
  //   },
  // },
  // performing type set here to avoid the type error
  validateSearch: (search: Record<string, string>) => {
    return {
      page: Number(search.page) || 1,
      type: Object.keys(lessonTitle).includes(search.type)
        ? search.type
        : 'system',
    } as { page: number; type: keyof typeof lessonTitle };
  },
  component: RouteComponent,
});

function RouteComponent() {
  const { page, type } = Route.useSearch();
  const { isPending, isError, error, data } = useGetLessonListQuery(type, page);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <main className='w-7xl mx-auto'>
      <Card>
        <CardHeader>
          <CardTitle className='text-3xl font-normal text-center pt-12'>
            {lessonTitle[type].title}
          </CardTitle>
          <CardDescription className='text-center pt-3'>
            {lessonTitle[type].description}
          </CardDescription>
        </CardHeader>
        <CardContent className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
          {data.data.map((lesson) => (
            <LessonItem
              key={lesson.id}
              lesson={lesson}
            />
          ))}
        </CardContent>
        <CardFooter>
          <Page meta={data.meta} />
        </CardFooter>
      </Card>
    </main>
  );
}
