import { type ICaptchaRef } from '@/components/form/Captcha';
import useAppForm from '@/components/form/FormContext';
import { Button } from '@/components/ui/button';
import { useRegisterMutation } from '@/services/auth';
import { createFileRoute } from '@tanstack/react-router';
import { useRef } from 'react';
import AuthLayout from './-components/AuthLayout';

interface IFormData {
  name: string;
  password: string;
  password_confirmation: string;
  captcha: string;
}

export const Route = createFileRoute('/auth/register')({
  component: RouteComponent,
});

function RouteComponent() {
  const loginMutation = useRegisterMutation<IFormData>();
  const captchaRef = useRef<ICaptchaRef | null>(null);

  const form = useAppForm({
    defaultValues: {
      name: 'admin123',
      password: 'password',
      password_confirmation: 'password',
      captcha: '',
    },
    onSubmit: async ({ value }) => {
      loginMutation.mutate(value, {
        onError: () => {
          captchaRef.current?.refresh();
        },
      });
    },
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <AuthLayout
        title='Sign Up'
        bgImage='/assets/images/auth/register.jpg'
      >
        <form.AppField
          name='name'
          children={(field) => (
            <field.TextField
              type='text'
              placeholder='Enter your account name'
            />
          )}
        />

        <form.AppField
          name='password'
          children={(field) => (
            <field.TextField
              type='password'
              placeholder='Enter your password'
            />
          )}
        />

        <form.AppField
          name='password_confirmation'
          children={(field) => (
            <field.TextField
              type='password'
              placeholder='Enter your confirm password'
            />
          )}
        />

        <form.AppField
          name='captcha'
          children={(field) => <field.Captcha ref={captchaRef} />}
        />

        <Button
          variant='default'
          className='w-full'
        >
          Sign Up
        </Button>
      </AuthLayout>
    </form>
  );
}
