import { type ICaptchaRef } from '@/components/form/Captcha';
import useAppForm from '@/components/form/FormContext';
import { Button } from '@/components/ui/button';
import { useForgetPasswordMutation } from '@/services/auth';
import { createFileRoute } from '@tanstack/react-router';
import { useRef } from 'react';
import AuthLayout from './-components/AuthLayout';

export const Route = createFileRoute('/auth/forget')({
  component: RouteComponent,
});

interface IFormData {
  password: string;
  password_confirmation: string;
  code: string;
  captcha: string;
  fieldType: 'email' | 'mobile';
}

function RouteComponent() {
  const forgetPasswordMutation = useForgetPasswordMutation<IFormData>();
  const captchaRef = useRef<ICaptchaRef | null>(null);

  const form = useAppForm({
    defaultValues: {
      email: '',
      mobile: '',
      password: '',
      password_confirmation: '',
      code: '',
      captcha: '',
      fieldType: 'email',
    },
    onSubmit: async ({ value }) => {
      forgetPasswordMutation.mutate(value as IFormData, {
        onError: () => {
          captchaRef.current?.refresh();
        },
      });
    },
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <AuthLayout
        title='Forget Password'
        bgImage='/assets/images/auth/forget-password.jpg'
      >
        <div className='flex gap-3 justify-center mb-3'>
          <form.AppField
            name='fieldType'
            children={(field) => (
              <Button
                variant={field.state.value === 'email' ? 'default' : 'outline'}
                size={'sm'}
                onClick={() => field.handleChange('email')}
                type='button'
              >
                Email
              </Button>
            )}
          />
          <form.AppField
            name='fieldType'
            children={(field) => (
              <Button
                variant={field.state.value === 'mobile' ? 'default' : 'outline'}
                size={'sm'}
                onClick={() => field.handleChange('mobile')}
                type='button'
              >
                Mobile
              </Button>
            )}
          />
        </div>
        <form.Subscribe
          selector={(state) => state.values.fieldType}
          children={(fieldType) => (
            <form.AppField
              name={fieldType as 'email' | 'mobile'}
              children={(field) => (
                <field.SendCode
                  field={fieldType as 'email' | 'mobile'}
                  showLabel={false}
                  action={`/auth/send-code/${fieldType}`}
                  placeholder={fieldType === 'email' ? 'Email' : 'Mobile'}
                />
              )}
            />
          )}
        />
        <form.AppField
          name='code'
          children={(field) => <field.TextField placeholder='Code' />}
        />
        <form.AppField
          name='password'
          children={(field) => (
            <field.TextField
              type='password'
              placeholder='Password'
            />
          )}
        />
        <form.AppField
          name='password_confirmation'
          children={(field) => (
            <field.TextField
              type='password'
              placeholder='Confirm password'
            />
          )}
        />

        <form.AppField
          name='captcha'
          children={(field) => <field.Captcha ref={captchaRef} />}
        />

        <Button
          variant='default'
          className='w-full'
        >
          Reset Password
        </Button>
      </AuthLayout>
    </form>
  );
}
