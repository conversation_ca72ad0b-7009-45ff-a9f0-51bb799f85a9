import { useValidateStore } from '@/store/useValidateStore';
import { createFileRoute, Outlet, useLocation } from '@tanstack/react-router';
import { useEffect } from 'react';

export const Route = createFileRoute('/auth')({
  component: RouteComponent,
});

function RouteComponent() {
  const match = useLocation();
  const clearErrors = useValidateStore((state) => state.clearErrors);

  useEffect(() => {
    clearErrors();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [match.pathname]);

  return <Outlet />;
}
