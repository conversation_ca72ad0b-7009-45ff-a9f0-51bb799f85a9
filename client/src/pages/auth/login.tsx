import { type ICaptchaRef } from '@/components/form/Captcha';
import useAppForm from '@/components/form/FormContext';
import { Button } from '@/components/ui/button';
import { useLoginMutation } from '@/services/auth';
import { createFileRoute } from '@tanstack/react-router';
import { useRef } from 'react';
import AuthLayout from './-components/AuthLayout';
export const Route = createFileRoute('/auth/login')({
  component: RouteComponent,
});

function RouteComponent() {
  const loginMutation = useLoginMutation();
  const captchaRef = useRef<ICaptchaRef | null>(null);

  const form = useAppForm({
    defaultValues: {
      account: 'admin',
      password: 'password',
      captcha: '',
    },
    onSubmit: async ({ value }) => {
      loginMutation.mutate(value, {
        onError: () => {
          captchaRef.current?.refresh();
        },
      });
    },
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <AuthLayout
        title='Login'
        bgImage='/assets/images/auth/login.jpg'
      >
        <form.AppField
          name='account'
          children={(field) => (
            <field.TextField
              type='text'
              placeholder='Enter your account'
            />
          )}
        />

        <form.AppField
          name='password'
          children={(field) => (
            <field.TextField
              type='password'
              placeholder='Enter your password'
            />
          )}
        />

        <form.AppField
          name='captcha'
          children={(field) => <field.Captcha ref={captchaRef} />}
        />

        <Button
          variant='default'
          className='w-full'
        >
          Login
        </Button>
      </AuthLayout>
    </form>
  );
}
