import { cn } from '@/lib/utils';
import { <PERSON> } from '@tanstack/react-router';
import type { FC, PropsWithChildren } from 'react';

interface Props {
  title: string;
  bgImage: string;
  className?: string;
}

const AuthLayout: FC<PropsWithChildren<Props>> = ({
  children,
  bgImage,
  title,
  className,
}) => {
  return (
    <main
      className={cn(
        'w-screen h-screen flex justify-center items-center bg-[#2c3e50]',
        className
      )}
    >
      <section className='grid grid-cols-2 rounded-xl'>
        <div className=' bg-white rounded-l-sm px-6 py-12 w-[350px]'>
          <h1 className='text-center mb-6'>{title}</h1>
          {children}
          <div className='flex flex-col gap-2 mt-6'>
            {title === 'Login' ? (
              <div className='text-sm text-muted-foreground'>
                Don't have an account? Go{' '}
                <Link
                  to='/auth/register'
                  className='underline'
                >
                  Register
                </Link>
              </div>
            ) : (
              <div className='text-sm text-muted-foreground'>
                Already have an account? Try{' '}
                <Link
                  to='/auth/login'
                  className='underline'
                >
                  Login
                </Link>
              </div>
            )}

            <div className='flex flex-row gap-2 items-center'>
              <Link to='/auth/forget'>
                <span className='text-sm text-muted-foreground underline'>
                  Forgot password?
                </span>
              </Link>
              <Link to='/'>
                <span className='text-sm text-muted-foreground underline'>
                  Back to home
                </span>
              </Link>
            </div>
          </div>
        </div>

        <div
          style={{
            backgroundImage: `url(${bgImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
          className='rounded-r-sm'
        ></div>
      </section>
    </main>
  );
};

export default AuthLayout;
