import Page from '@/components/common/Page';
import Error from '@/components/error/Error';
import Loading from '@/components/loading/Loading';
import TopicItem from '@/components/topic/TopicItem';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useGetTopicListQuery } from '@/services/topic';
import type { IUser } from '@/type/user';
import type { FC } from 'react';

interface Props {
  user: IUser;
}

const TopicList: FC<Props> = ({ user }) => {
  const {
    data: topicList,
    isPending,
    isError,
    error,
  } = useGetTopicListQuery({ page: 1, uid: user.id });

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <Card className='xl:w-7xl mx-auto mt-3'>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle>Topic List</CardTitle>
      </CardHeader>
      <CardContent>
        {topicList.data.map((topic) => (
          <TopicItem
            key={topic.id}
            topic={topic}
          />
        ))}
      </CardContent>
      <CardFooter>
        <Page
          meta={topicList.meta}
          singlePageShow={false}
        />
      </CardFooter>
    </Card>
  );
};
export default TopicList;
