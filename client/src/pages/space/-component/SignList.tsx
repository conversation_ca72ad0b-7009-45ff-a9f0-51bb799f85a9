import Page from '@/components/common/Page';
import Error from '@/components/error/Error';
import Loading from '@/components/loading/Loading';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useGetUserSignListQuery } from '@/services/sign';
import type { IUser } from '@/type/user';
import dayjs from 'dayjs';
import type { FC } from 'react';

interface Props {
  user: IUser;
}

const SignList: FC<Props> = ({ user }) => {
  const {
    data: signList,
    isPending,
    isError,
    error,
  } = useGetUserSignListQuery(user.id);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <Card className='2xl:w-7xl mx-auto mt-3'>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle>Sign List</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Feeling</TableHead>
              <TableHead className='w-[30px]'>Sign Time</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {signList.data.map((sign) => (
              <TableRow key={sign.id}>
                <TableCell className='font-medium'>{sign.content}</TableCell>
                <TableCell>
                  {dayjs(sign.created_at).format('HH:mm DD/MM/YYYY')}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
      <CardFooter>
        <Page
          meta={signList.meta}
          singlePageShow={false}
        />
      </CardFooter>
    </Card>
  );
};
export default SignList;
