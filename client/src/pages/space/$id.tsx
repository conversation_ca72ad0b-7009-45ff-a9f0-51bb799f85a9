import Error from '@/components/error/Error';
import Loading from '@/components/loading/Loading';
import { Button } from '@/components/ui/button';
import { useGetUserInfoQuery } from '@/services/user';
import { createFileRoute, Link } from '@tanstack/react-router';
import dayjs from 'dayjs';
import SignList from './-component/SignList';
import TopicList from './-component/TopicList';

export const Route = createFileRoute('/space/$id')({
  component: RouteComponent,
  params: {
    parse: (params: Record<string, unknown>) => ({
      id: Number(params.id),
    }),
  },
  validateSearch: (search: Record<string, unknown>) => {
    return { type: search.type || 'sign' } as { type?: 'sign' | 'topic' };
  },
});

function RouteComponent() {
  const { id } = Route.useParams();
  const { type } = Route.useSearch();
  const { data: user, isPending, isError, error } = useGetUserInfoQuery(id);

  if (isPending) return <Loading />;
  if (isError) return <Error error={error} />;

  return (
    <main>
      <section className='bg-amber-600 -mt-6 flex flex-col items-center justify-center py-12'>
        <img
          src={user.avatar}
          alt={user.name}
          className='w-32 h-32 rounded-full object-cover'
        />

        <div className='flex flex-col items-center justify-center text-muted text-xs mt-6'>
          {user.name}
          <div>registered at {dayjs(user.created_at).fromNow()}</div>
        </div>

        <div className='flex items-center gap-2 mt-12'>
          <Link
            to='/space/$id'
            params={{ id: user.id }}
          >
            <Button
              variant={type === 'sign' ? 'default' : 'secondary'}
              size={'sm'}
            >
              Sign
            </Button>
          </Link>
          <Link
            to='/space/$id'
            params={{ id: user.id }}
            search={{ type: 'topic' }}
          >
            <Button
              variant={type === 'topic' ? 'default' : 'secondary'}
              size={'sm'}
            >
              Post
            </Button>
          </Link>
        </div>
      </section>
      {type === 'sign' ? <SignList user={user} /> : <TopicList user={user} />}
    </main>
  );
}
