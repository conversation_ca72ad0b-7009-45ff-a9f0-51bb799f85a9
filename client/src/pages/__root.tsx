import type { IUserAuth } from '@/hooks/useAuth';
import { createRootRouteWithContext, Outlet } from '@tanstack/react-router';

interface MyRouterContext {
  auth: IUserAuth;
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  component: () => (
    <>
      {/* <div className='p-2 flex gap-2'>
        <Link
          to='/'
          className='[&.active]:font-bold'
        >
          Home
        </Link>{' '}
        <Link
          to='/about'
          className='[&.active]:font-bold'
        >
          About
        </Link>
      </div>
      <hr /> */}
      <Outlet />
      {/* <TanStackRouterDevtools /> */}
    </>
  ),
});
