import useAppForm from '@/components/form/FormContext';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
// import { z } from 'zod';
import MemberLayout from './MemberLayout';
import { useBindUserEmailMutation } from '@/services/user';

interface IFormData {
  email: string;
  code: string;
}
const Email = () => {
  const { user } = useAuth();
  const bindUserEmailMutation = useBindUserEmailMutation<IFormData>();

  const form = useAppForm({
    defaultValues: {
      email: '',
      code: '',
    },
    onSubmit: ({ value }) => {
      bindUserEmailMutation.mutate(value);
    },
  });

  return (
    <main className=''>
      <MemberLayout
        description={
          user('email')
            ? `You already have an email linked to your account. ${user(
                'email'
              )}`
            : 'Please enter your email to bind your account.'
        }
      >
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          {/* <form.AppField
            name='email'
            validators={{ onSubmit: z.string().email() }}
            children={(field) => (
              <field.TextField
                fieldLabel='Email'
                type='email'
                placeholder='Enter your email'
              >
                <Button
                  variant='outline'
                  onClick={() => {
                    const email = form.getFieldValue('email');
                    if (!email) return toast('Please enter your email');
                    sendCodeMutation.mutate({ email });
                  }}
                >
                  Send Code
                </Button>
              </field.TextField>
            )}
          /> */}
          <form.AppField
            name='email'
            // validators={{
            //   onSubmit: z
            //     .string()
            //     .min(1, 'Email is required')
            //     .email('Please enter a valid email'),
            // }}
            children={(field) => (
              <field.SendCode
                field='email'
                action='/user/send-code/email'
                placeholder='Enter your email'
              />
            )}
          />
          <form.AppField
            name='code'
            // validators={{
            //   onSubmit: z.string().min(4, 'Code must be 4 digits'),
            // }}
            children={(field) => (
              <field.TextField
                fieldLabel='Code'
                type='text'
                placeholder='Enter the code'
              />
            )}
          />
          <Button variant={'outline'}>Submit</Button>
        </form>
      </MemberLayout>
    </main>
  );
};
export default Email;
