import { memberMenus } from '@/config/menu';
import { useAuth } from '@/hooks/useAuth';
import { Link } from '@tanstack/react-router';
import dayjs from 'dayjs';

const LeftMenu = () => {
  const { user } = useAuth();

  return (
    <main>
      <div className='bg-white rounded-lg overflow-hidden'>
        <img
          src={user('avatar')}
          alt=''
        />
        <div className='px-2 py-3 flex flex-col items-center'>
          <div className=''>{user('nickname')}</div>
          <div className='text-muted-foreground text-xs'>
            {dayjs(user('created_at')).fromNow()}
          </div>
          <div className='text-muted-foreground text-xs'>UID: {user('id')}</div>
        </div>
      </div>
      <div className='flex flex-col bg-white mt-3 rounded-lg overflow-hidden'>
        {memberMenus.map((menu, index) => (
          <Link
            to={menu.to}
            key={index}
            className='px-2 py-3 border-b text-sm'
            activeProps={{
              className: 'text-white bg-primary hover:text-white!',
            }}
          >
            {menu.title}
          </Link>
        ))}
      </div>
    </main>
  );
};
export default LeftMenu;
