import useAppForm from '@/components/form/FormContext';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { useUpdateUserProfileMutation } from '@/services/user';

const BaseInfo = () => {
  const { getUserInfo } = useAuth();
  const updateUserProfileMutation = useUpdateUserProfileMutation();

  const form = useAppForm({
    defaultValues: getUserInfo(),
    onSubmit: ({ value }) => {
      updateUserProfileMutation.mutate(value);
    },
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <form.AppField
        name='nickname'
        children={(field) => <field.TextField fieldLabel='Nickname' />}
      />
      <form.AppField
        name='sex'
        children={(field) => (
          <field.RadioField
            label='Gender'
            options={[
              { label: 'Male', value: '1', id: 'male' },
              { label: 'Female', value: '2', id: 'female' },
            ]}
          />
        )}
      />
      <form.AppField
        name='home'
        children={(field) => <field.TextField fieldLabel='Website' />}
      />
      <form.AppField
        name='weibo'
        children={(field) => <field.TextField fieldLabel='Weibo' />}
      />
      <form.AppField
        name='github'
        children={(field) => <field.TextField fieldLabel='Github' />}
      />
      <form.AppField
        name='wechat'
        children={(field) => <field.TextField fieldLabel='Wechat' />}
      />
      <form.AppField
        name='qq'
        children={(field) => <field.TextField fieldLabel='QQ' />}
      />
      <Button
        variant='outline'
        type='submit'
      >
        Save
      </Button>
    </form>
  );
};
export default BaseInfo;
