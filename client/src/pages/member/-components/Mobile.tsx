import useAppForm from '@/components/form/FormContext';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
// import { z } from 'zod';
import MemberLayout from './MemberLayout';
import { useBindUserMobileMutation } from '@/services/user';

interface IFormData {
  mobile: string;
  code: string;
}
const Mobile = () => {
  const { user } = useAuth();
  const bindUserMobileMutation = useBindUserMobileMutation<IFormData>();

  const form = useAppForm({
    defaultValues: {
      mobile: '',
      code: '',
    },
    onSubmit: ({ value }) => {
      bindUserMobileMutation.mutate(value);
    },
  });

  return (
    <main className=''>
      <MemberLayout
        description={
          user('mobile')
            ? `You already have an mobile number linked to your account. ${user(
                'mobile'
              )}`
            : 'Please enter your mobile number to bind your account.'
        }
      >
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          <form.AppField
            name='mobile'
            children={(field) => (
              <field.SendCode
                field='mobile'
                action='/user/send-code/mobile'
                placeholder='Enter your mobile number'
              />
            )}
          />
          <form.AppField
            name='code'
            children={(field) => (
              <field.TextField
                fieldLabel='Code'
                type='text'
                placeholder='Enter the code'
              />
            )}
          />
          <Button variant={'outline'}>Submit</Button>
        </form>
      </MemberLayout>
    </main>
  );
};
export default Mobile;
