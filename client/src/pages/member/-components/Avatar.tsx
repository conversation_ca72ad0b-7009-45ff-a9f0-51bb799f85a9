import { RcUpload } from '@/components/upload/RcUpload';
import { useAuth } from '@/hooks/useAuth';
import { useUpdateUserProfileMutation } from '@/services/user';

const Avatar = () => {
  const { user, getUserInfo } = useAuth();

  const updateUserProfileMutation = useUpdateUserProfileMutation();
  return (
    <main className=''>
      <div className='w-52 rounded-lg overflow-hidden'>
        <RcUpload
          action='/user/upload/avatar'
          onSuccess={(data) => {
            updateUserProfileMutation.mutate({
              ...getUserInfo(),
              avatar: data.url,
            });
          }}
          accept='.jpeg,.jpg,.png'
        >
          <img
            src={user('avatar')}
            alt='avatar'
            className='w-52 h-52 object-cover'
          />
        </RcUpload>
        <div className='text-center text-muted-foreground text-sm mt-2'>
          Please upload your avatar
        </div>
      </div>
    </main>
  );
};
export default Avatar;
