import useAppForm from '@/components/form/FormContext';
import { Button } from '@/components/ui/button';
import { useUpdateUserPasswordMutation } from '@/services/user';

interface IFormData {
  oldPassword: string;
  password: string;
  password_confirmation: string;
}
const Password = () => {
  const updatePasswordMutation = useUpdateUserPasswordMutation<IFormData>();
  const form = useAppForm({
    defaultValues: {
      oldPassword: '',
      password: '',
      password_confirmation: '',
    },
    onSubmit: ({ value }) => {
      updatePasswordMutation.mutate(value);
    },
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <form.AppField
        name='oldPassword'
        children={(field) => (
          <field.TextField
            fieldLabel='Old Password'
            type='password'
          />
        )}
      />
      <form.AppField
        name='password'
        children={(field) => (
          <field.TextField
            fieldLabel='New Password'
            type='password'
          />
        )}
      />
      <form.AppField
        name='password_confirmation'
        children={(field) => (
          <field.TextField
            fieldLabel='Confirm Password'
            type='password'
          />
        )}
      />
      <Button
        variant={'outline'}
        type='submit'
      >
        Update
      </Button>
    </form>
  );
};
export default Password;
