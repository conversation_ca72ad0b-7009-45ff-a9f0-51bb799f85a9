import { Info } from '@icon-park/react';
import type { FC, PropsWithChildren } from 'react';

interface Props {
  description?: string;
}
const MemberLayout: FC<PropsWithChildren<Props>> = ({
  description,
  children,
}) => {
  return (
    <main className=''>
      {description && (
        <div className='text-muted-foreground text-sm border px-3 py-2 rounded-md flex items-center gap-2 mb-3 bg-muted'>
          <Info /> {description}
        </div>
      )}

      <div className='border p-3 rounded-md'>{children}</div>
    </main>
  );
};
export default MemberLayout;
