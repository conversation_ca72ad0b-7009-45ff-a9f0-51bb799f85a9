import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { createFileRoute } from '@tanstack/react-router';
import Email from './-components/Email';
import Mobile from './-components/Mobile';
import Weixin from './-components/Weixin';

export const Route = createFileRoute('/member/Link')({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <Tabs defaultValue='email'>
      <TabsList>
        <TabsTrigger value='email'>Link Email</TabsTrigger>
        <TabsTrigger value='mobile'>Link Phone</TabsTrigger>
        <TabsTrigger value='weixin'>Link Wechat</TabsTrigger>
      </TabsList>

      <TabsContent value='email'>
        <Email />
      </TabsContent>
      <TabsContent value='mobile'>
        <Mobile />
      </TabsContent>
      <TabsContent value='weixin'>
        <Weixin />
      </TabsContent>
    </Tabs>
  );
}
