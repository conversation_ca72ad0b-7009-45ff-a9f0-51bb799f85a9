import Footer from '@/components/common/Footer';
import FrontNavbar from '@/components/navbar/FrontNavbar';
import { createFileRoute, Outlet, redirect } from '@tanstack/react-router';
import LeftMenu from './-components/LeftMenu';
import { toast } from 'sonner';

export const Route = createFileRoute('/member')({
  component: RouteComponent,
  beforeLoad: ({ context }) => {
    if (!context.auth.isAuthenticated()) {
      toast.error('Please login to access this page');

      throw redirect({
        to: '/auth/login',
      });
    }
  },
});

function RouteComponent() {
  return (
    <main>
      <FrontNavbar />
      <section className='xl:w-7xl mx-auto'>
        <div className='grid grid-cols-[200px_1fr] gap-6 pt-4'>
          <LeftMenu />
          <div className='bg-white rounded-lg p-4'>
            <Outlet />
          </div>
        </div>
      </section>
      <Footer />
    </main>
  );
}
