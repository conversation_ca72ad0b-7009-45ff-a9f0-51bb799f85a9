import { Card, CardContent } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { createFileRoute } from '@tanstack/react-router';
import BaseInfo from './-components/BaseInfo';
import Password from './-components/Password';
import Avatar from './-components/Avatar';

export const Route = createFileRoute('/member/info')({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <Tabs defaultValue='info'>
      <TabsList>
        <TabsTrigger value='info'>My Profile</TabsTrigger>
        <TabsTrigger value='password'>Change Password</TabsTrigger>
        <TabsTrigger value='avatar'>Avatar</TabsTrigger>
      </TabsList>
      <Card className='mt-3'>
        <CardContent>
          <TabsContent value='info'>
            <BaseInfo />
          </TabsContent>
          <TabsContent value='password'>
            <Password />
          </TabsContent>
          <TabsContent value='avatar'>
            <Avatar />
          </TabsContent>
        </CardContent>
      </Card>
    </Tabs>
  );
}
