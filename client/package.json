{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@icon-park/react": "^1.4.2", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-form": "^1.10.0", "@tanstack/react-query": "^5.75.5", "@tanstack/react-router": "^1.120.3", "@tanstack/react-router-devtools": "^1.120.3", "@types/qs": "^6.14.0", "axios": "^1.9.0", "cherry-markdown": "^0.9.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "lucide-react": "^0.508.0", "qs": "^6.14.0", "rc-upload": "^4.9.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-spinners": "^0.17.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "vaul": "^1.1.2", "xgplayer": "^3.0.22", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.74.7", "@tanstack/router-plugin": "^1.120.3", "@types/node": "^22.15.17", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "sass-embedded": "^1.87.0", "tw-animate-css": "^1.2.9", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "pnpm": {"onlyBuiltDependencies": ["@swc/core", "@tailwindcss/oxide", "core-js", "es5-ext", "esbuild"]}}